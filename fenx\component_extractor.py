#!/usr/bin/env python3
"""
Claude Code UI组件代码提取器和复现工具
提取混淆组件的实际代码并尝试还原其功能
"""

import re
import json
import os

class ComponentCodeExtractor:
    def __init__(self, file_path):
        self.file_path = file_path
        self.content = ""
        self.components = {}
        
        # 基于分析项目的组件映射
        self.known_components = {
            'y2A': {
                'name': 'WelcomeComponent',
                'desc': '欢迎界面渲染器',
                'expected': 'Welcome to Claude Code'
            },
            'k2A': {
                'name': 'StatsDisplayComponent', 
                'desc': 'GA统计展示',
                'expected': 'Claude Code is now generally available'
            },
            'j2A': {
                'name': 'ToolStatsComponent',
                'desc': '工具使用统计组件',
                'expected': ['Read', 'Edit', 'Bash', 'Grep', 'Write']
            },
            'Fq5': {
                'name': 'ProgressBarComponent',
                'desc': '进度条组件',
                'expected': 'backgroundColor'
            },
            'c9': {
                'name': 'TerminalSizeHook',
                'desc': '终端尺寸管理器',
                'expected': 'process.stdout.columns'
            },
            'BE': {
                'name': 'ContentDisplayComponent',
                'desc': '通用内容显示组件',
                'expected': 'verbose'
            }
        }
        
    def load_file(self):
        """加载文件"""
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
            self.content = f.read()
        print(f"文件已加载: {len(self.content):,} 字符")
        
    def extract_function_body(self, func_name, max_depth=2000):
        """提取函数体 - 处理混淆代码的复杂结构"""
        patterns = [
            # function funcName() { ... }
            rf'function\s+{func_name}\s*\([^)]*\)\s*{{',
            # var funcName = function() { ... }
            rf'var\s+{func_name}\s*=\s*function\s*\([^)]*\)\s*{{',
            # funcName = function() { ... }
            rf'{func_name}\s*=\s*function\s*\([^)]*\)\s*{{',
            # const funcName = () => { ... }
            rf'const\s+{func_name}\s*=\s*\([^)]*\)\s*=>\s*{{',
            # let funcName = () => { ... }
            rf'let\s+{func_name}\s*=\s*\([^)]*\)\s*=>\s*{{',
        ]
        
        for pattern in patterns:
            match = re.search(pattern, self.content)
            if match:
                start = match.start()
                # 提取函数体 - 需要匹配大括号
                brace_count = 0
                in_function = False
                end = start
                
                for i in range(start, min(start + max_depth, len(self.content))):
                    char = self.content[i]
                    if char == '{':
                        brace_count += 1
                        in_function = True
                    elif char == '}':
                        brace_count -= 1
                        if in_function and brace_count == 0:
                            end = i + 1
                            break
                            
                if end > start:
                    return self.content[start:end]
                    
        # 如果没找到函数定义，尝试查找变量赋值
        var_pattern = rf'var\s+{func_name}\s*=\s*[^;]+;'
        match = re.search(var_pattern, self.content)
        if match:
            return match.group(0)
            
        return None
        
    def analyze_component_code(self, func_name, code):
        """分析组件代码，提取关键信息"""
        info = {
            'has_createElement': 'createElement' in code,
            'has_useState': 'useState' in code,
            'has_useEffect': 'useEffect' in code,
            'returns_jsx': 'return' in code and ('createElement' in code or '.createElement' in code),
            'props_pattern': None,
            'state_vars': [],
            'render_elements': []
        }
        
        # 查找props模式
        props_match = re.search(r'function[^(]*\(([^)]+)\)', code)
        if props_match:
            info['props_pattern'] = props_match.group(1)
            
        # 查找state变量
        state_pattern = r'\[(\w+),\s*(\w+)\]\s*=\s*[^.]+\.useState'
        state_matches = re.findall(state_pattern, code)
        info['state_vars'] = state_matches
        
        # 查找createElement调用
        create_pattern = r'\.createElement\s*\(\s*([^,\s]+)'
        create_matches = re.findall(create_pattern, code)
        info['render_elements'] = list(set(create_matches))[:10]
        
        return info
        
    def extract_all_components(self):
        """提取所有已知组件的代码"""
        print("\n提取UI组件代码...")
        
        for func_name, meta in self.known_components.items():
            print(f"\n处理 {func_name} ({meta['desc']})...")
            
            # 提取函数代码
            code = self.extract_function_body(func_name)
            if code:
                # 分析代码
                analysis = self.analyze_component_code(func_name, code)
                
                # 查找相关字符串
                has_expected = False
                if isinstance(meta['expected'], list):
                    has_expected = any(exp in code for exp in meta['expected'])
                else:
                    has_expected = meta['expected'] in code
                    
                self.components[func_name] = {
                    'meta': meta,
                    'code': code[:1000] + '...' if len(code) > 1000 else code,
                    'full_code': code,
                    'analysis': analysis,
                    'has_expected_content': has_expected,
                    'code_length': len(code)
                }
                
                print(f"  - 代码长度: {len(code)} 字符")
                print(f"  - 包含createElement: {analysis['has_createElement']}")
                print(f"  - 包含预期内容: {has_expected}")
            else:
                print(f"  - 未找到函数定义")
                
    def generate_reconstructed_components(self):
        """基于分析结果生成重建的组件代码"""
        output_dir = os.path.dirname(self.file_path) + '/fenx/reconstructed'
        os.makedirs(output_dir, exist_ok=True)
        
        # 生成主文件
        main_file = os.path.join(output_dir, 'reconstructed_components.jsx')
        with open(main_file, 'w', encoding='utf-8') as f:
            f.write("""// Claude Code UI组件复现
// 基于混淆代码分析结果的重建版本

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text } from 'ink';

""")
            
            # 1. 欢迎组件
            f.write("""// 欢迎界面组件 (原始: y2A)
export function WelcomeComponent() {
  return (
    <Text>
      <Text color="claude">✻ </Text>
      <Text>Welcome to Claude Code</Text>
    </Text>
  );
}

""")
            
            # 2. GA统计组件
            f.write("""// GA统计展示组件 (原始: k2A)
export function StatsDisplayComponent() {
  return (
    <Box flexDirection="column" gap={1}>
      <Text>Claude Code is now generally available. Thank you for making it possible 🙏</Text>
      <Text>Here's a glimpse at all of the community's contributions:</Text>
    </Box>
  );
}

""")
            
            # 3. 工具统计组件
            f.write("""// 工具使用统计组件 (原始: j2A)
export function ToolStatsComponent({ stats, width }) {
  const defaultStats = [
    { toolName: "Read", usesTx: "47.5M", usesN: 47500000 },
    { toolName: "Edit", usesTx: "39.3M", usesN: 39300000 },
    { toolName: "Bash", usesTx: "17.9M", usesN: 17900000 },
    { toolName: "Grep", usesTx: "14.7M", usesN: 14700000 },
    { toolName: "Write", usesTx: "6.8M", usesN: 6800000 }
  ];
  
  const data = stats || defaultStats;
  const maxUses = Math.max(...data.map(d => d.usesN));
  
  return (
    <Box flexDirection="column" gap={1}>
      {data.map((stat, i) => (
        <Box key={i} flexDirection="row">
          <Text>{stat.toolName.padEnd(10)}</Text>
          <ProgressBar 
            width={width - 15} 
            percent={stat.usesN / maxUses} 
            text={stat.usesTx}
          />
        </Box>
      ))}
    </Box>
  );
}

""")
            
            # 4. 进度条组件
            f.write("""// 进度条组件 (原始: Fq5)
export function ProgressBar({ width, percent, text }) {
  const filledWidth = Math.ceil(width * percent);
  const emptyWidth = width - filledWidth;
  
  const filledBar = text + ' '.repeat(Math.max(0, filledWidth - text.length - 1));
  const emptyBar = ' '.repeat(Math.max(0, emptyWidth));
  
  return (
    <Text>
      <Text backgroundColor="claude">{filledBar}</Text>
      <Text backgroundColor="secondaryBorder">{emptyBar}</Text>
    </Text>
  );
}

""")
            
            # 5. 终端尺寸Hook
            f.write("""// 终端尺寸管理Hook (原始: c9)
export function useTerminalSize() {
  const [size, setSize] = useState({
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24
  });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        columns: process.stdout.columns || 80,
        rows: process.stdout.rows || 24
      });
    };
    
    process.stdout.on('resize', handleResize);
    return () => process.stdout.off('resize', handleResize);
  }, []);
  
  return size;
}

""")
            
            # 6. 内容显示组件
            f.write("""// 通用内容显示组件 (原始: BE)
export function ContentDisplay({ content, verbose = false, isError = false }) {
  const { columns } = useTerminalSize();
  
  const displayContent = verbose 
    ? content 
    : truncateContent(content, columns);
    
  return (
    <Box>
      <Text color={isError ? 'error' : undefined}>
        {displayContent}
      </Text>
    </Box>
  );
}

function truncateContent(content, maxWidth) {
  if (content.length <= maxWidth) return content;
  return content.slice(0, maxWidth - 3) + '...';
}

""")
            
            # 颜色主题
            f.write("""// 颜色主题定义
export const colorTheme = {
  claude: '#FF6B35',
  success: '#00C851',
  error: '#FF4444',  
  warning: '#FFBB33',
  remember: '#33B5E5',
  bashBorder: '#AA7942',
  secondaryBorder: '#666666',
  text: '#FFFFFF',
  permission: '#9C27B0'
};
""")
            
        print(f"\n重建的组件已保存到: {main_file}")
        
        # 生成示例应用
        demo_file = os.path.join(output_dir, 'demo_app.jsx')
        with open(demo_file, 'w', encoding='utf-8') as f:
            f.write("""// Claude Code UI组件演示应用
import React from 'react';
import { render } from 'ink';
import { 
  WelcomeComponent, 
  StatsDisplayComponent, 
  ToolStatsComponent,
  useTerminalSize 
} from './reconstructed_components.jsx';

function App() {
  const { columns } = useTerminalSize();
  
  return (
    <Box flexDirection="column" gap={1}>
      <WelcomeComponent />
      <StatsDisplayComponent />
      <ToolStatsComponent width={columns} />
    </Box>
  );
}

render(<App />);
""")
            
        print(f"演示应用已保存到: {demo_file}")
        
        # 生成分析报告
        self.generate_extraction_report(output_dir)
        
    def generate_extraction_report(self, output_dir):
        """生成详细的提取报告"""
        report_file = os.path.join(output_dir, 'component_extraction_report.md')
        
        with open(report_file, 'w', encoding='utf-8') as f:
            f.write("# Claude Code UI组件提取和复现报告\n\n")
            
            f.write("## 组件提取结果\n\n")
            
            for func_name, data in self.components.items():
                f.write(f"### {func_name} - {data['meta']['name']}\n")
                f.write(f"**描述**: {data['meta']['desc']}\n\n")
                f.write(f"**代码信息**:\n")
                f.write(f"- 代码长度: {data['code_length']} 字符\n")
                f.write(f"- 包含createElement: {'是' if data['analysis']['has_createElement'] else '否'}\n")
                f.write(f"- 包含useState: {'是' if data['analysis']['has_useState'] else '否'}\n")
                f.write(f"- 包含预期内容: {'是' if data['has_expected_content'] else '否'}\n")
                
                if data['analysis']['props_pattern']:
                    f.write(f"- Props参数: `{data['analysis']['props_pattern']}`\n")
                    
                if data['analysis']['state_vars']:
                    f.write(f"- State变量: {data['analysis']['state_vars']}\n")
                    
                if data['analysis']['render_elements']:
                    f.write(f"- 渲染元素: {', '.join(data['analysis']['render_elements'][:5])}\n")
                    
                f.write(f"\n**代码片段**:\n```javascript\n{data['code'][:500]}...\n```\n\n")
                
            f.write("## 复现说明\n\n")
            f.write("基于混淆代码的分析，我们重建了以下组件：\n\n")
            f.write("1. **WelcomeComponent**: 显示欢迎信息\n")
            f.write("2. **StatsDisplayComponent**: 显示GA统计信息\n")
            f.write("3. **ToolStatsComponent**: 显示工具使用统计\n")
            f.write("4. **ProgressBar**: 进度条组件\n")
            f.write("5. **useTerminalSize**: 终端尺寸管理Hook\n")
            f.write("6. **ContentDisplay**: 通用内容显示组件\n\n")
            
            f.write("## 运行方式\n\n")
            f.write("```bash\n")
            f.write("# 安装依赖\n")
            f.write("npm install react ink\n\n")
            f.write("# 运行演示\n")
            f.write("node demo_app.jsx\n")
            f.write("```\n")
            
        print(f"提取报告已保存到: {report_file}")
        
        # 保存JSON数据
        json_file = os.path.join(output_dir, 'extracted_components.json')
        json_data = {
            func_name: {
                'meta': data['meta'],
                'analysis': data['analysis'],
                'code_length': data['code_length'],
                'has_expected_content': data['has_expected_content']
            }
            for func_name, data in self.components.items()
        }
        
        with open(json_file, 'w', encoding='utf-8') as f:
            json.dump(json_data, f, indent=2, ensure_ascii=False)

def main():
    extractor = ComponentCodeExtractor(r"E:\claude\yuan\package\cli.js")
    
    print("开始提取UI组件代码...")
    extractor.load_file()
    extractor.extract_all_components()
    extractor.generate_reconstructed_components()
    
    print("\n提取和复现完成！")

if __name__ == "__main__":
    main()