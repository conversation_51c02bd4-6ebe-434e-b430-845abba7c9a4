// 重构的Claude Code UI组件
import React from 'react';

// 样式常量
const styles = {
  style0: {
    "color": "inherit"
},
  style1: {
    "width": "0",
    "height": "0"
},
  style2: {
    "width": "g.width",
    "height": "g.height"
},
  style3: {
    "height": "m.height-i.borderTop-i.borderBottom-i.paddingTop-i.paddingBottom+",
    "width": "m.width-i.borderLeft-i.borderRight-i.paddingLeft-i.paddingRight+"
},
  style4: {
    "display": "flex",
    "backgroundColor": "#333740",
    "borderRadius": "2px",
    "fontWeight": "bold",
    "padding": "3px 5px",
    "fontSize": "12px"
},
  style5: {
    "color": "#ee78e6"
},
  style6: {
    "color": "#d7d7d7"
},
  style7: {
    "width": "n.width",
    "height": "n.height"
},
  style8: {
    "height": "A0.bottom-A0.top",
    "width": "A0.right-A0.left"
},
  style9: {
    "height": "this.tipBoundsWindow.innerHeight",
    "width": "this.tipBoundsWindow.innerWidth"
},
  style10: {
    "padding": "rgba(77",
    "margin": "rgba(255",
    "border": "rgba(255"
},
  style11: {
    "color": "n"
},
  style12: {
    "height": "i",
    "width": "s1"
},
  style13: {
    "color": "red;\n    xx-opacity: 0.5;\n    bottom: 0;\n    left: 0;\n    pointer-events: none;\n    position: fixed;\n    right: 0;\n    top: 0;\n    z-index: 1000000000;\n  `;var S=window.document.documentElement;S.insertBefore(FW"
},
  style14: {
    "color": "black;"
},
  style15: {
    "color": "purple; font-weight: bold;"
},
  style16: {
    "color": "green; font-weight: bold;"
},
  style17: {
    "width": "y0",
    "height": "FA",
    "margin": "iA",
    "padding": "xB"
},
  style18: {
    "color": "teal; font-weight: bold;"
},
  style19: {
    "color": "^4.2.3"
},
  style20: {
    "width": "-1",
    "height": "-1"
},
  style21: {
    "color": "[...B.color"
},
  style22: {
    "padding": "QM.constants.RSA_PKCS1_PSS_PADDING"
},
  style23: {
    "padding": "QM.constants.RSA_PKCS1_PSS_PADDING"
},
  style24: {
    "width": "String",
    "height": "String"
},
  style25: {
    "color": "String",
    "width": "String"
},
  style26: {
    "width": "String",
    "height": "String"
},
  style27: {
    "height": "{type:"
},
  style28: {
    "width": "String",
    "height": "String"
},
  style29: {
    "width": "{type:"
},
  style30: {
    "border": "String",
    "width": "String"
},
  style31: {
    "width": "{type:"
},
  style32: {
    "width": "{type:"
},
  style33: {
    "width": "String",
    "height": "String"
},
  style34: {
    "color": "{type:String"
},
  style35: {
    "height": "I"
},
  style36: {
    "width": "F??NaN",
    "height": "I??NaN"
},
  style37: {
    "width": "0",
    "height": "0"
},
  style38: {
    "width": "Q",
    "height": "D"
},
  style39: {
    "width": "Q",
    "height": "D"
},
  style40: {
    "width": "B",
    "height": "Q"
},
  style41: {
    "width": "A.yogaNode.getComputedWidth()",
    "height": "A.yogaNode.getComputedHeight()"
},
  style42: {
    "width": "A.staticNode.yogaNode.getComputedWidth()",
    "height": "A.staticNode.yogaNode.getComputedHeight()"
},
  style43: {
    "height": "G"
},
  style44: {
    "color": "A",
    "backgroundColor": "B"
},
  style45: {
    "flexDirection": "column",
    "padding": "1"
},
  style46: {
    "backgroundColor": "error",
    "color": "text"
},
  style47: {
    "flexDirection": "column"
},
  style48: {
    "width": "G+1"
},
  style49: {
    "backgroundColor": "F===Q.line?",
    "color": "F===Q.line?"
},
  style50: {
    "backgroundColor": "F===Q.line?",
    "color": "F===Q.line?"
},
  style51: {
    "flexDirection": "column"
},
  style52: {
    "color": "secondaryText"
},
  style53: {
    "width": "A.yogaNode?.getComputedWidth()??0",
    "height": "A.yogaNode?.getComputedHeight()??0"
},
  style54: {
    "color": "b$2[A]"
},
  style55: {
    "flexDirection": "column"
},
  style56: {
    "backgroundColor": "A"
},
  style57: {
    "color": "black"
},
  style58: {
    "color": "green"
},
  style59: {
    "color": "blue"
},
  style60: {
    "color": "Q"
},
  style61: {
    "color": "magenta"
},
  style62: {
    "color": "green"
},
  style63: {
    "color": "blue"
},
  style64: {
    "color": "Q"
},
  style65: {
    "color": "blue"
},
  style66: {
    "color": "ed4[A]"
},
  style67: {
    "color": "secondaryText"
},
  style68: {
    "color": "secondaryText"
},
  style69: {
    "flexDirection": "column",
    "padding": "1",
    "width": "70"
},
  style70: {
    "flexDirection": "column",
    "padding": "1"
},
  style71: {
    "flexDirection": "column"
},
  style72: {
    "height": "B"
},
  style73: {
    "flexDirection": "row",
    "height": "B"
},
  style74: {
    "height": "1"
},
  style75: {
    "color": "error"
},
  style76: {
    "color": "F"
},
  style77: {
    "color": "F"
},
  style78: {
    "height": "1"
},
  style79: {
    "color": "error"
},
  style80: {
    "color": "secondaryText"
},
  style81: {
    "width": "xL.default(Q)"
},
  style82: {
    "color": "secondaryText"
},
  style83: {
    "color": "secondaryText"
},
  style84: {
    "color": "secondaryText"
},
  style85: {
    "color": "secondaryText"
},
  style86: {
    "color": "secondaryText"
},
  style87: {
    "flexDirection": "column"
},
  style88: {
    "color": "secondaryText"
},
  style89: {
    "color": "success"
},
  style90: {
    "color": "warning"
},
  style91: {
    "color": "secondaryText"
},
  style92: {
    "flexDirection": "column"
},
  style93: {
    "color": "claude"
},
  style94: {
    "color": "ide"
},
  style95: {
    "color": "secondaryText"
},
  style96: {
    "color": "warning"
},
  style97: {
    "flexDirection": "column"
},
  style98: {
    "color": "suggestion"
},
  style99: {
    "color": "suggestion"
},
  style100: {
    "color": "diffAddedWord"
},
  style101: {
    "color": "diffRemovedWord"
},
  style102: {
    "color": "secondaryText"
},
  style103: {
    "color": "secondaryText"
},
  style104: {
    "color": "secondaryText"
},
  style105: {
    "color": "Q?"
},
  style106: {
    "justifyContent": "space-between",
    "width": "100%"
},
  style107: {
    "height": "1"
},
  style108: {
    "justifyContent": "space-between",
    "width": "100%"
},
  style109: {
    "height": "1"
},
  style110: {
    "color": "secondaryText"
},
  style111: {
    "justifyContent": "space-between",
    "width": "100%"
},
  style112: {
    "color": "secondaryText"
},
  style113: {
    "justifyContent": "space-between",
    "width": "100%"
},
  style114: {
    "height": "1"
},
  style115: {
    "color": "secondaryText"
},
  style116: {
    "color": "error"
},
  style117: {
    "color": "error"
},
  style118: {
    "height": "1"
},
  style119: {
    "height": "1"
},
  style120: {
    "color": "error"
},
  style121: {
    "height": "1"
},
  style122: {
    "height": "1"
},
  style123: {
    "height": "1"
},
  style124: {
    "height": "1"
},
  style125: {
    "color": "error"
},
  style126: {
    "color": "secondaryText"
},
  style127: {
    "flexDirection": "column"
},
  style128: {
    "color": "planMode"
},
  style129: {
    "color": "secondaryText"
},
  style130: {
    "flexDirection": "column"
},
  style131: {
    "color": "permission"
},
  style132: {
    "color": "error"
},
  style133: {
    "flexDirection": "column"
},
  style134: {
    "color": "error"
},
  style135: {
    "color": "success"
},
  style136: {
    "color": "error"
},
  style137: {
    "width": "Z"
},
  style138: {
    "width": "J"
},
  style139: {
    "width": "Q"
},
  style140: {
    "backgroundColor": "D?"
},
  style141: {
    "color": "G?"
},
  style142: {
    "width": "Q"
},
  style143: {
    "backgroundColor": "D?"
},
  style144: {
    "color": "G?"
},
  style145: {
    "color": "F?",
    "backgroundColor": "K"
},
  style146: {
    "width": "X"
},
  style147: {
    "width": "X"
},
  style148: {
    "color": "F?",
    "backgroundColor": "D?"
},
  style149: {
    "width": "X"
},
  style150: {
    "color": "F?",
    "backgroundColor": "D?"
},
  style151: {
    "width": "X"
},
  style152: {
    "color": "F?"
},
  style153: {
    "width": "B"
},
  style154: {
    "color": "secondaryText"
},
  style155: {
    "flexDirection": "column"
},
  style156: {
    "flexDirection": "column"
},
  style157: {
    "flexDirection": "column"
},
  style158: {
    "flexDirection": "column"
},
  style159: {
    "flexDirection": "column",
    "width": "100%"
},
  style160: {
    "flexDirection": "column"
},
  style161: {
    "color": "remember"
},
  style162: {
    "flexDirection": "column"
},
  style163: {
    "flexDirection": "column",
    "padding": "1"
},
  style164: {
    "color": "warning"
},
  style165: {
    "flexDirection": "column"
},
  style166: {
    "flexDirection": "column"
},
  style167: {
    "height": "2"
},
  style168: {
    "width": "44"
},
  style169: {
    "color": "y?"
},
  style170: {
    "color": "y?"
},
  style171: {
    "color": "y?"
},
  style172: {
    "color": "y?"
},
  style173: {
    "color": "y?"
},
  style174: {
    "color": "permission"
},
  style175: {
    "flexDirection": "column"
},
  style176: {
    "color": "secondaryText"
},
  style177: {
    "flexDirection": "column"
},
  style178: {
    "color": "secondaryText"
},
  style179: {
    "color": "secondaryText"
},
  style180: {
    "flexDirection": "column"
},
  style181: {
    "color": "D?"
},
  style182: {
    "flexDirection": "column"
},
  style183: {
    "color": "secondaryText"
},
  style184: {
    "color": "error"
},
  style185: {
    "color": "secondaryText"
},
  style186: {
    "color": "secondaryText"
},
  style187: {
    "color": "warning"
},
  style188: {
    "color": "secondaryText"
},
  style189: {
    "flexDirection": "column"
},
  style190: {
    "color": "secondaryText"
},
  style191: {
    "color": "secondaryText"
},
  style192: {
    "flexDirection": "column"
},
  style193: {
    "color": "warning"
},
  style194: {
    "color": "secondaryText"
},
  style195: {
    "color": "warning"
},
  style196: {
    "color": "warning"
},
  style197: {
    "color": "text"
},
  style198: {
    "color": "ide"
},
  style199: {
    "color": "ide"
},
  style200: {
    "color": "secondaryText"
},
  style201: {
    "color": "secondaryText"
},
  style202: {
    "color": "secondaryText"
},
  style203: {
    "color": "secondaryText"
},
  style204: {
    "color": "error"
},
  style205: {
    "color": "secondaryText"
},
  style206: {
    "color": "secondaryText"
},
  style207: {
    "color": "secondaryText"
},
  style208: {
    "height": "1",
    "width": "2"
},
  style209: {
    "color": "L"
},
  style210: {
    "flexDirection": "column",
    "width": "100%",
    "alignItems": "flex-start"
},
  style211: {
    "flexDirection": "row",
    "width": "100%"
},
  style212: {
    "color": "L"
},
  style213: {
    "flexDirection": "row",
    "alignItems": "flex-start",
    "width": "100%"
},
  style214: {
    "color": "L"
},
  style215: {
    "color": "L"
},
  style216: {
    "height": "1",
    "width": "2"
},
  style217: {
    "color": "secondaryText"
},
  style218: {
    "width": "2"
},
  style219: {
    "color": "secondaryText"
},
  style220: {
    "width": "2"
},
  style221: {
    "color": "secondaryText"
},
  style222: {
    "width": "2"
},
  style223: {
    "color": "secondaryText"
},
  style224: {
    "height": "1",
    "width": "2"
},
  style225: {
    "color": "secondaryText"
},
  style226: {
    "height": "1",
    "width": "2"
},
  style227: {
    "color": "Q===!1?"
},
  style228: {
    "flexDirection": "column",
    "padding": "1",
    "width": "100%"
},
  style229: {
    "flexDirection": "row",
    "justifyContent": "space-between"
},
  style230: {
    "color": "remember"
},
  style231: {
    "flexDirection": "column"
},
  style232: {
    "flexDirection": "column"
},
  style233: {
    "color": "text"
},
  style234: {
    "color": "secondaryText"
},
  style235: {
    "color": "secondaryText"
},
  style236: {
    "flexDirection": "column"
},
  style237: {
    "color": "remember"
},
  style238: {
    "flexDirection": "column",
    "padding": "1"
},
  style239: {
    "color": "claude"
},
  style240: {
    "flexDirection": "column"
},
  style241: {
    "flexDirection": "column"
},
  style242: {
    "flexDirection": "column"
},
  style243: {
    "color": "secondaryText"
},
  style244: {
    "color": "secondaryText"
},
  style245: {
    "color": "secondaryText"
},
  style246: {
    "color": "secondaryText"
},
  style247: {
    "color": "secondaryText"
},
  style248: {
    "flexDirection": "column"
},
  style249: {
    "color": "secondaryText"
},
  style250: {
    "flexDirection": "column"
},
  style251: {
    "flexDirection": "column",
    "width": "100%"
},
  style252: {
    "color": "ide"
},
  style253: {
    "flexDirection": "column"
},
  style254: {
    "flexDirection": "column"
},
  style255: {
    "flexDirection": "column",
    "width": "100%"
},
  style256: {
    "color": "ide"
},
  style257: {
    "flexDirection": "column"
},
  style258: {
    "flexDirection": "column"
},
  style259: {
    "flexDirection": "column"
},
  style260: {
    "flexDirection": "column",
    "width": "100%"
},
  style261: {
    "color": "ide"
},
  style262: {
    "flexDirection": "column"
},
  style263: {
    "color": "claude"
},
  style264: {
    "flexDirection": "column",
    "padding": "1"
},
  style265: {
    "color": "warning"
},
  style266: {
    "flexDirection": "column"
},
  style267: {
    "color": "error"
},
  style268: {
    "color": "error"
},
  style269: {
    "color": "suggestion"
},
  style270: {
    "flexDirection": "column"
},
  style271: {
    "flexDirection": "column",
    "width": "70"
},
  style272: {
    "color": "secondaryText"
},
  style273: {
    "color": "secondaryText"
},
  style274: {
    "flexDirection": "column"
},
  style275: {
    "flexDirection": "column",
    "width": "70"
},
  style276: {
    "flexDirection": "column",
    "padding": "0"
},
  style277: {
    "padding": "1"
},
  style278: {
    "color": "claude"
},
  style279: {
    "flexDirection": "column"
},
  style280: {
    "color": "text"
},
  style281: {
    "flexDirection": "column"
},
  style282: {
    "color": "text"
},
  style283: {
    "color": "permission"
},
  style284: {
    "color": "success"
},
  style285: {
    "color": "error"
},
  style286: {
    "color": "permission"
},
  style287: {
    "flexDirection": "column"
},
  style288: {
    "width": "1000"
},
  style289: {
    "flexDirection": "column"
},
  style290: {
    "color": "success"
},
  style291: {
    "width": "1000"
},
  style292: {
    "color": "warning"
},
  style293: {
    "flexDirection": "column"
},
  style294: {
    "color": "claude"
},
  style295: {
    "flexDirection": "column"
},
  style296: {
    "color": "secondaryText"
},
  style297: {
    "color": "secondaryText"
},
  style298: {
    "flexDirection": "column"
},
  style299: {
    "color": "secondaryText"
},
  style300: {
    "color": "secondaryText"
},
  style301: {
    "color": "secondaryText"
},
  style302: {
    "color": "error"
},
  style303: {
    "color": "secondaryText"
},
  style304: {
    "color": "secondaryText"
},
  style305: {
    "color": "secondaryText"
},
  style306: {
    "flexDirection": "column"
},
  style307: {
    "flexDirection": "column"
},
  style308: {
    "color": "B?"
},
  style309: {
    "color": "!B||!A?"
},
  style310: {
    "flexDirection": "column"
},
  style311: {
    "flexDirection": "column"
},
  style312: {
    "color": "permission"
},
  style313: {
    "color": "claude"
},
  style314: {
    "flexDirection": "column"
},
  style315: {
    "flexDirection": "column"
},
  style316: {
    "color": "warning"
},
  style317: {
    "flexDirection": "column"
},
  style318: {
    "flexDirection": "column"
},
  style319: {
    "flexDirection": "column"
},
  style320: {
    "flexDirection": "column"
},
  style321: {
    "color": "W==="
},
  style322: {
    "flexDirection": "column"
},
  style323: {
    "flexDirection": "column"
},
  style324: {
    "color": "success"
},
  style325: {
    "color": "success"
},
  style326: {
    "color": "success"
},
  style327: {
    "flexDirection": "column"
},
  style328: {
    "flexDirection": "column"
},
  style329: {
    "color": "error"
},
  style330: {
    "flexDirection": "column"
},
  style331: {
    "color": "claude"
},
  style332: {
    "flexDirection": "column"
},
  style333: {
    "flexDirection": "column"
},
  style334: {
    "flexDirection": "column"
},
  style335: {
    "color": "claude"
},
  style336: {
    "color": "claude"
},
  style337: {
    "flexDirection": "column"
},
  style338: {
    "flexDirection": "column"
},
  style339: {
    "flexDirection": "column"
},
  style340: {
    "color": "warning"
},
  style341: {
    "flexDirection": "column"
},
  style342: {
    "color": "permission"
},
  style343: {
    "color": "claude"
},
  style344: {
    "flexDirection": "column",
    "width": "100%"
},
  style345: {
    "flexDirection": "column"
},
  style346: {
    "flexDirection": "column"
},
  style347: {
    "flexDirection": "row"
},
  style348: {
    "color": "error"
},
  style349: {
    "flexDirection": "column"
},
  style350: {
    "flexDirection": "column"
},
  style351: {
    "width": "1000"
},
  style352: {
    "color": "success"
},
  style353: {
    "color": "error"
},
  style354: {
    "color": "permission"
},
  style355: {
    "flexDirection": "column"
},
  style356: {
    "flexDirection": "column"
},
  style357: {
    "flexDirection": "column"
},
  style358: {
    "color": "secondaryText"
},
  style359: {
    "color": "secondaryText"
},
  style360: {
    "flexDirection": "column"
},
  style361: {
    "flexDirection": "column"
},
  style362: {
    "color": "success"
},
  style363: {
    "flexDirection": "column"
},
  style364: {
    "flexDirection": "column"
},
  style365: {
    "flexDirection": "column"
},
  style366: {
    "flexDirection": "column"
},
  style367: {
    "flexDirection": "column"
},
  style368: {
    "color": "success"
},
  style369: {
    "flexDirection": "column"
},
  style370: {
    "color": "claude"
},
  style371: {
    "flexDirection": "row"
},
  style372: {
    "flexDirection": "column"
},
  style373: {
    "color": "warning"
},
  style374: {
    "flexDirection": "column"
},
  style375: {
    "color": "error"
},
  style376: {
    "flexDirection": "column"
},
  style377: {
    "flexDirection": "column"
},
  style378: {
    "flexDirection": "column"
},
  style379: {
    "color": "text"
},
  style380: {
    "flexDirection": "column"
},
  style381: {
    "color": "secondaryText"
},
  style382: {
    "color": "secondaryText"
},
  style383: {
    "color": "secondaryText"
},
  style384: {
    "color": "secondaryText"
},
  style385: {
    "flexDirection": "column",
    "padding": "1"
},
  style386: {
    "color": "claude"
},
  style387: {
    "flexDirection": "column"
},
  style388: {
    "color": "secondaryText"
},
  style389: {
    "color": "secondaryText"
},
  style390: {
    "color": "secondaryText"
},
  style391: {
    "color": "error"
},
  style392: {
    "flexDirection": "column"
},
  style393: {
    "color": "secondaryText"
},
  style394: {
    "color": "secondaryText"
},
  style395: {
    "flexDirection": "column"
},
  style396: {
    "color": "secondaryText"
},
  style397: {
    "color": "success"
},
  style398: {
    "color": "error"
},
  style399: {
    "color": "secondaryText"
},
  style400: {
    "color": "secondaryText"
},
  style401: {
    "color": "secondaryText"
},
  style402: {
    "flexDirection": "column"
},
  style403: {
    "flexDirection": "column"
},
  style404: {
    "flexDirection": "column"
},
  style405: {
    "color": "secondaryText"
},
  style406: {
    "color": "secondaryText"
},
  style407: {
    "color": "secondaryText"
},
  style408: {
    "flexDirection": "column",
    "height": "B-1"
},
  style409: {
    "color": "text"
},
  style410: {
    "color": "text"
},
  style411: {
    "color": "text"
},
  style412: {
    "color": "text"
},
  style413: {
    "color": "text"
},
  style414: {
    "color": "secondaryText"
},
  style415: {
    "height": "1"
},
  style416: {
    "color": "secondaryText"
},
  style417: {
    "flexDirection": "column"
},
  style418: {
    "height": "1"
},
  style419: {
    "color": "secondaryText"
},
  style420: {
    "height": "5",
    "flexDirection": "column"
},
  style421: {
    "color": "secondaryText"
},
  style422: {
    "height": "1"
},
  style423: {
    "color": "secondaryText"
},
  style424: {
    "height": "1"
},
  style425: {
    "color": "secondaryText"
},
  style426: {
    "color": "success"
},
  style427: {
    "color": "error"
},
  style428: {
    "color": "secondaryText"
},
  style429: {
    "flexDirection": "column",
    "width": "100%",
    "padding": "1"
},
  style430: {
    "color": "secondaryText"
},
  style431: {
    "color": "secondaryText"
},
  style432: {
    "color": "secondaryText"
},
  style433: {
    "width": "100%",
    "flexDirection": "column"
},
  style434: {
    "width": "100%"
},
  style435: {
    "flexDirection": "column",
    "padding": "1",
    "width": "100%"
},
  style436: {
    "color": "permission"
},
  style437: {
    "flexDirection": "column"
},
  style438: {
    "color": "permission"
},
  style439: {
    "color": "success"
},
  style440: {
    "color": "error"
},
  style441: {
    "flexDirection": "column"
},
  style442: {
    "flexDirection": "column",
    "height": "7"
},
  style443: {
    "flexDirection": "column"
},
  style444: {
    "color": "error"
},
  style445: {
    "flexDirection": "column",
    "height": "3"
},
  style446: {
    "color": "error"
},
  style447: {
    "color": "error"
},
  style448: {
    "width": "100%",
    "flexDirection": "column"
},
  style449: {
    "flexDirection": "column",
    "padding": "1",
    "width": "100%"
},
  style450: {
    "color": "permission"
},
  style451: {
    "flexDirection": "column"
},
  style452: {
    "color": "secondaryText"
},
  style453: {
    "color": "secondaryText"
},
  style454: {
    "color": "secondaryText"
},
  style455: {
    "color": "secondaryText"
},
  style456: {
    "flexDirection": "column"
},
  style457: {
    "flexDirection": "column"
},
  style458: {
    "flexDirection": "column"
},
  style459: {
    "height": "1"
},
  style460: {
    "color": "secondaryText"
},
  style461: {
    "height": "1"
},
  style462: {
    "height": "1"
},
  style463: {
    "flexDirection": "column"
},
  style464: {
    "color": "permission"
},
  style465: {
    "flexDirection": "column"
},
  style466: {
    "flexDirection": "row"
},
  style467: {
    "flexDirection": "row"
},
  style468: {
    "color": "permission"
},
  style469: {
    "backgroundColor": "A===Q?",
    "color": "A===Q?"
},
  style470: {
    "flexDirection": "column"
},
  style471: {
    "color": "permission"
},
  style472: {
    "color": "error"
},
  style473: {
    "flexDirection": "column"
},
  style474: {
    "color": "error"
},
  style475: {
    "flexDirection": "column"
},
  style476: {
    "color": "secondaryText"
},
  style477: {
    "flexDirection": "column"
},
  style478: {
    "flexDirection": "column"
},
  style479: {
    "color": "permission"
},
  style480: {
    "flexDirection": "column"
},
  style481: {
    "color": "error"
},
  style482: {
    "flexDirection": "column"
},
  style483: {
    "flexDirection": "column"
},
  style484: {
    "color": "error"
},
  style485: {
    "flexDirection": "column"
},
  style486: {
    "color": "success"
},
  style487: {
    "flexDirection": "column"
},
  style488: {
    "flexDirection": "column"
},
  style489: {
    "flexDirection": "column"
},
  style490: {
    "color": "warning"
},
  style491: {
    "flexDirection": "column"
},
  style492: {
    "color": "error"
},
  style493: {
    "color": "warning"
},
  style494: {
    "flexDirection": "column"
},
  style495: {
    "color": "suggestion"
},
  style496: {
    "flexDirection": "column"
},
  style497: {
    "color": "success"
},
  style498: {
    "flexDirection": "column"
},
  style499: {
    "color": "success"
},
  style500: {
    "flexDirection": "column"
},
  style501: {
    "color": "error"
},
  style502: {
    "color": "warning"
},
  style503: {
    "color": "warning"
},
  style504: {
    "flexDirection": "column"
},
  style505: {
    "color": "warning"
},
  style506: {
    "color": "warning"
},
  style507: {
    "flexDirection": "column"
},
  style508: {
    "color": "error"
},
  style509: {
    "flexDirection": "column"
},
  style510: {
    "color": "text"
},
  style511: {
    "flexDirection": "column"
},
  style512: {
    "color": "success"
},
  style513: {
    "flexDirection": "column"
},
  style514: {
    "color": "error"
},
  style515: {
    "flexDirection": "column"
},
  style516: {
    "color": "secondaryText"
},
  style517: {
    "color": "secondaryText"
},
  style518: {
    "color": "secondaryText"
},
  style519: {
    "width": "Z-12"
},
  style520: {
    "color": "secondaryText"
},
  style521: {
    "color": "remember"
},
  style522: {
    "color": "error"
},
  style523: {
    "color": "error"
},
  style524: {
    "width": "Q-12"
},
  style525: {
    "color": "secondaryText"
},
  style526: {
    "color": "secondaryText"
},
  style527: {
    "color": "error"
},
  style528: {
    "color": "error"
},
  style529: {
    "width": "G-12"
},
  style530: {
    "color": "secondaryText"
},
  style531: {
    "height": "1"
},
  style532: {
    "color": "error"
},
  style533: {
    "color": "error"
},
  style534: {
    "color": "error"
},
  style535: {
    "flexDirection": "column"
},
  style536: {
    "color": "error"
},
  style537: {
    "height": "1"
},
  style538: {
    "height": "1"
},
  style539: {
    "color": "$"
},
  style540: {
    "height": "1"
},
  style541: {
    "color": "error"
},
  style542: {
    "color": "error"
},
  style543: {
    "height": "1"
},
  style544: {
    "width": "F"
},
  style545: {
    "flexDirection": "row",
    "width": "F"
},
  style546: {
    "width": "I"
},
  style547: {
    "width": "I"
},
  style548: {
    "color": "B?"
},
  style549: {
    "flexDirection": "row",
    "justifyContent": "space-between",
    "width": "100%"
},
  style550: {
    "flexDirection": "row"
},
  style551: {
    "color": "K"
},
  style552: {
    "backgroundColor": "$"
},
  style553: {
    "color": "error"
},
  style554: {
    "height": "1"
},
  style555: {
    "color": "error"
},
  style556: {
    "height": "1"
},
  style557: {
    "color": "error"
},
  style558: {
    "height": "1"
},
  style559: {
    "color": "error"
},
  style560: {
    "height": "1"
},
  style561: {
    "color": "error"
},
  style562: {
    "height": "1"
},
  style563: {
    "color": "error"
},
  style564: {
    "color": "error"
},
  style565: {
    "height": "1"
},
  style566: {
    "color": "error"
},
  style567: {
    "alignItems": "flex-start",
    "flexDirection": "row",
    "justifyContent": "space-between",
    "width": "100%"
},
  style568: {
    "color": "text"
},
  style569: {
    "flexDirection": "column",
    "width": "D-6"
},
  style570: {
    "flexDirection": "column",
    "width": "100%"
},
  style571: {
    "color": "bashBorder"
},
  style572: {
    "color": "secondaryText"
},
  style573: {
    "flexDirection": "column",
    "width": "100%"
},
  style574: {
    "color": "secondaryText"
},
  style575: {
    "flexDirection": "row",
    "width": "100%"
},
  style576: {
    "width": "2"
},
  style577: {
    "color": "secondaryText"
},
  style578: {
    "flexDirection": "column",
    "width": "Q-4"
},
  style579: {
    "color": "secondaryText"
},
  style580: {
    "flexDirection": "column",
    "width": "100%"
},
  style581: {
    "color": "remember"
},
  style582: {
    "color": "remember"
},
  style583: {
    "height": "1"
},
  style584: {
    "color": "secondaryText"
},
  style585: {
    "color": "text"
},
  style586: {
    "color": "error"
},
  style587: {
    "height": "1"
},
  style588: {
    "flexDirection": "column",
    "width": "100%"
},
  style589: {
    "color": "secondaryText"
},
  style590: {
    "color": "secondaryText"
},
  style591: {
    "color": "secondaryText"
},
  style592: {
    "color": "secondaryText"
},
  style593: {
    "color": "secondaryText"
},
  style594: {
    "color": "secondaryText"
},
  style595: {
    "color": "secondaryText"
},
  style596: {
    "flexDirection": "row",
    "width": "100%"
},
  style597: {
    "flexDirection": "column",
    "width": "D-10"
},
  style598: {
    "color": "warning"
},
  style599: {
    "width": "V"
},
  style600: {
    "flexDirection": "column",
    "width": "100%"
},
  style601: {
    "width": "V"
},
  style602: {
    "flexDirection": "column",
    "width": "100%"
},
  style603: {
    "width": "Y-5"
},
  style604: {
    "width": "J"
},
  style605: {
    "width": "J"
},
  style606: {
    "flexDirection": "column"
},
  style607: {
    "height": "1"
},
  style608: {
    "color": "success"
},
  style609: {
    "height": "1"
},
  style610: {
    "height": "1"
},
  style611: {
    "color": "secondaryText"
},
  style612: {
    "flexDirection": "column"
},
  style613: {
    "color": "success"
},
  style614: {
    "color": "secondaryText"
},
  style615: {
    "color": "secondaryText"
},
  style616: {
    "justifyContent": "space-between",
    "width": "100%"
},
  style617: {
    "height": "1"
},
  style618: {
    "color": "${Z"
},
  style619: {
    "width": "A="
},
  style620: {
    "width": "A"
},
  style621: {
    "width": "B=",
    "padding": "Q=0"
},
  style622: {
    "width": "B"
},
  style623: {
    "color": "Z"
},
  style624: {
    "flexDirection": "column"
},
  style625: {
    "flexDirection": "column"
},
  style626: {
    "color": "B"
},
  style627: {
    "color": "secondaryText"
},
  style628: {
    "flexDirection": "column"
},
  style629: {
    "color": "V?"
},
  style630: {
    "color": "V?"
},
  style631: {
    "color": "s"
},
  style632: {
    "color": "s"
},
  style633: {
    "color": "y?"
},
  style634: {
    "flexDirection": "column"
},
  style635: {
    "color": "secondaryText"
},
  style636: {
    "flexDirection": "column"
},
  style637: {
    "color": "secondaryText"
},
  style638: {
    "color": "secondaryText"
},
  style639: {
    "flexDirection": "column"
},
  style640: {
    "flexDirection": "column"
},
  style641: {
    "color": "secondaryText"
},
  style642: {
    "flexDirection": "column"
},
  style643: {
    "flexDirection": "column"
},
  style644: {
    "color": "F===0?"
},
  style645: {
    "color": "b?"
},
  style646: {
    "color": "secondaryText"
},
  style647: {
    "color": "Y?"
},
  style648: {
    "backgroundColor": "F"
},
  style649: {
    "backgroundColor": "G"
},
  style650: {
    "color": "suggestion"
},
  style651: {
    "color": "error"
},
  style652: {
    "flexDirection": "column"
},
  style653: {
    "flexDirection": "column"
},
  style654: {
    "flexDirection": "column"
},
  style655: {
    "color": "error"
},
  style656: {
    "color": "error"
},
  style657: {
    "color": "c1"
},
  style658: {
    "flexDirection": "column"
},
  style659: {
    "flexDirection": "column"
},
  style660: {
    "color": "warning"
},
  style661: {
    "flexDirection": "column"
},
  style662: {
    "color": "error"
},
  style663: {
    "color": "error"
},
  style664: {
    "color": "error"
},
  style665: {
    "flexDirection": "column"
},
  style666: {
    "color": "L===F?"
},
  style667: {
    "color": "error"
},
  style668: {
    "color": "N"
},
  style669: {
    "color": "secondaryText"
},
  style670: {
    "color": "warning"
},
  style671: {
    "backgroundColor": "F"
},
  style672: {
    "flexDirection": "column"
},
  style673: {
    "color": "text"
},
  style674: {
    "color": "warning"
},
  style675: {
    "color": "warning"
},
  style676: {
    "color": "secondaryText"
},
  style677: {
    "color": "warning"
},
  style678: {
    "color": "warning"
},
  style679: {
    "color": "secondaryText"
},
  style680: {
    "flexDirection": "row"
},
  style681: {
    "color": "warning"
},
  style682: {
    "color": "warning"
},
  style683: {
    "flexDirection": "row"
},
  style684: {
    "color": "warning"
},
  style685: {
    "color": "warning"
},
  style686: {
    "flexDirection": "row"
},
  style687: {
    "color": "warning"
},
  style688: {
    "color": "warning"
},
  style689: {
    "flexDirection": "column"
},
  style690: {
    "color": "warning"
},
  style691: {
    "color": "warning"
},
  style692: {
    "flexDirection": "column"
},
  style693: {
    "color": "warning"
},
  style694: {
    "color": "warning"
},
  style695: {
    "flexDirection": "column"
},
  style696: {
    "color": "secondaryText"
},
  style697: {
    "flexDirection": "column"
},
  style698: {
    "color": "secondaryText"
},
  style699: {
    "flexDirection": "column"
},
  style700: {
    "color": "secondaryText"
},
  style701: {
    "color": "suggestion"
},
  style702: {
    "color": "text"
},
  style703: {
    "flexDirection": "row"
},
  style704: {
    "color": "warning"
},
  style705: {
    "color": "warning"
},
  style706: {
    "width": "100%",
    "flexDirection": "column"
},
  style707: {
    "flexDirection": "column",
    "padding": "1",
    "width": "100%"
},
  style708: {
    "color": "permission"
},
  style709: {
    "flexDirection": "column"
},
  style710: {
    "flexDirection": "column"
},
  style711: {
    "flexDirection": "row"
},
  style712: {
    "color": "error"
},
  style713: {
    "flexDirection": "column",
    "height": "I?void 0:4+Math.min(Bf1"
},
  style714: {
    "flexDirection": "column"
},
  style715: {
    "flexDirection": "row",
    "height": "2"
},
  style716: {
    "width": "7"
},
  style717: {
    "color": "permission"
},
  style718: {
    "height": "1",
    "width": "100"
},
  style719: {
    "width": "100%"
},
  style720: {
    "flexDirection": "column"
},
  style721: {
    "color": "permission"
},
  style722: {
    "flexDirection": "column"
},
  style723: {
    "flexDirection": "column",
    "padding": "1"
},
  style724: {
    "color": "permission"
},
  style725: {
    "flexDirection": "column"
},
  style726: {
    "color": "secondaryText"
},
  style727: {
    "flexDirection": "column"
},
  style728: {
    "justifyContent": "flex-end"
},
  style729: {
    "justifyContent": "flex-end"
},
  style730: {
    "justifyContent": "flex-end"
},
  style731: {
    "flexDirection": "column",
    "alignItems": "flex-end"
},
  style732: {
    "flexDirection": "column"
},
  style733: {
    "flexDirection": "column"
},
  style734: {
    "color": "secondaryText"
},
  style735: {
    "justifyContent": "flex-end"
},
  style736: {
    "flexDirection": "column"
},
  style737: {
    "flexDirection": "column"
},
  style738: {
    "color": "secondaryText"
},
  style739: {
    "color": "secondaryText"
},
  style740: {
    "flexDirection": "column"
},
  style741: {
    "color": "secondaryText"
},
  style742: {
    "flexDirection": "column"
},
  style743: {
    "flexDirection": "column"
},
  style744: {
    "flexDirection": "column"
},
  style745: {
    "flexDirection": "column"
},
  style746: {
    "flexDirection": "column"
},
  style747: {
    "color": "secondaryText"
},
  style748: {
    "width": "F"
},
  style749: {
    "flexDirection": "column"
},
  style750: {
    "flexDirection": "column"
},
  style751: {
    "color": "secondaryText"
},
  style752: {
    "flexDirection": "column"
},
  style753: {
    "flexDirection": "column"
},
  style754: {
    "width": "F"
},
  style755: {
    "color": "secondaryText"
},
  style756: {
    "flexDirection": "column"
},
  style757: {
    "width": "G-12"
},
  style758: {
    "flexDirection": "column"
},
  style759: {
    "flexDirection": "column"
},
  style760: {
    "flexDirection": "column"
},
  style761: {
    "color": "secondaryText"
},
  style762: {
    "color": "D.content.color"
},
  style763: {
    "color": "remember"
},
  style764: {
    "color": "bashBorder"
},
  style765: {
    "color": "planMode"
},
  style766: {
    "color": "secondaryText"
},
  style767: {
    "color": "autoAccept"
},
  style768: {
    "color": "secondaryText"
},
  style769: {
    "color": "D?"
},
  style770: {
    "color": "text"
},
  style771: {
    "color": "success"
},
  style772: {
    "color": "error"
},
  style773: {
    "color": "secondaryText"
},
  style774: {
    "color": "success"
},
  style775: {
    "color": "error"
},
  style776: {
    "color": "E0().autoCompactEnabled?"
},
  style777: {
    "color": "error"
},
  style778: {
    "color": "ide"
},
  style779: {
    "color": "error"
},
  style780: {
    "color": "secondaryText"
},
  style781: {
    "color": "ide"
},
  style782: {
    "color": "ide"
},
  style783: {
    "color": "text"
},
  style784: {
    "color": "ide"
},
  style785: {
    "color": "secondaryText"
},
  style786: {
    "color": "warning"
},
  style787: {
    "color": "warning"
},
  style788: {
    "color": "error"
},
  style789: {
    "color": "error"
},
  style790: {
    "color": "warning"
},
  style791: {
    "color": "warning"
},
  style792: {
    "width": "Z?void 0:G"
},
  style793: {
    "color": "Q?"
},
  style794: {
    "width": "D-(Z?4:G+4)"
},
  style795: {
    "color": "Q?"
},
  style796: {
    "flexDirection": "row"
},
  style797: {
    "flexDirection": "column",
    "width": "22"
},
  style798: {
    "flexDirection": "column",
    "width": "35"
},
  style799: {
    "flexDirection": "row",
    "justifyContent": "space-between"
},
  style800: {
    "flexDirection": "column"
},
  style801: {
    "flexDirection": "column",
    "width": "C5-4"
},
  style802: {
    "color": "secondaryText"
},
  style803: {
    "alignItems": "flex-start",
    "justifyContent": "flex-start",
    "width": "100%"
},
  style804: {
    "alignItems": "flex-start",
    "justifyContent": "flex-start",
    "width": "3"
},
  style805: {
    "color": "bashBorder"
},
  style806: {
    "color": "remember"
},
  style807: {
    "color": "F?"
},
  style808: {
    "alignItems": "center",
    "width": "100%"
},
  style809: {
    "flexDirection": "column",
    "width": "100%"
},
  style810: {
    "flexDirection": "column",
    "padding": "1"
},
  style811: {
    "color": "success"
},
  style812: {
    "color": "warning"
},
  style813: {
    "flexDirection": "column",
    "padding": "1"
},
  style814: {
    "color": "warning"
},
  style815: {
    "flexDirection": "column",
    "padding": "1"
},
  style816: {
    "color": "warning"
},
  style817: {
    "flexDirection": "column",
    "padding": "1"
},
  style818: {
    "color": "warning"
},
  style819: {
    "flexDirection": "column",
    "padding": "1"
},
  style820: {
    "color": "error"
},
  style821: {
    "flexDirection": "column"
},
  style822: {
    "color": "warning"
},
  style823: {
    "color": "secondaryText"
},
  style824: {
    "flexDirection": "column"
},
  style825: {
    "color": "claude"
},
  style826: {
    "color": "warning"
},
  style827: {
    "color": "claude"
},
  style828: {
    "color": "claude"
},
  style829: {
    "color": "success"
},
  style830: {
    "color": "success"
},
  style831: {
    "flexDirection": "column"
},
  style832: {
    "color": "secondaryText"
},
  style833: {
    "color": "claude"
},
  style834: {
    "color": "secondaryText"
},
  style835: {
    "color": "text"
},
  style836: {
    "flexDirection": "column"
},
  style837: {
    "color": "secondaryText"
},
  style838: {
    "color": "claude"
},
  style839: {
    "color": "secondaryText"
},
  style840: {
    "color": "error"
},
  style841: {
    "color": "error"
},
  style842: {
    "color": "error"
},
  style843: {
    "color": "secondaryText"
}
};

// 重构的组件

function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HG.default.createElement(v,null,HG.default.createElement(P,{backgroundColor:"error",color:"text"}," ","ERROR"," "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HG.default.createElement(P,{dimColor:!0},D,":",Q.line,":",Q.column
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z.map(({line:F,value:I}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HG.default.createElement(v,{width:G+1},HG.default.createElement(P,{dimColor:F!==Q.line,backgroundColor:F===Q.line?"error":void 0,color:F===Q.line?"text":void 0},String(F
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "F===Q.line?error:void 0",
  "color": "F===Q.line?text:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " "+I
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A.stack.split(`
`
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HG.default.createElement(P,{dimColor:!0},"- "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HG.default.createElement(P,{dimColor:!0},"- "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    I.function
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","(",tr0(I.file
  );
}


function Kp1Component(props) {
  const styles = {};
  
  return React.createElement(
    "kp1component",
    { style: styles, ...props },
    cq.default.createElement(lV1.Provider,{value:{stdin:this.props.stdin,setRawMode:this.handleSetRawMode,isRawModeSupported:this.isRawModeSupported(
  );
}


function RV1Component(props) {
  const styles = {};
  
  return React.createElement(
    "rv1component",
    { style: styles, ...props },
    A
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    lr.default.createElement(P,{...Q.marker(
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    A
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    A
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    AA.pointer
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    AA.tick
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    I.visibleOptions.map((W
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    Q
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    ir.default.createElement(P,{...Q.marker(
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    A
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    ww.default.Children.map(A,(G,F
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    AA.pointer," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.arrowDown," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.arrowUp," "
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    AA.tick
  );
}


function VComponent() {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles },
    W.visibleOptions.map((C,K
  );
}


function PComponent() {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles },
    Q
  );
}


function Yq2Component(props) {
  const styles = {};
  
  return React.createElement(
    "yq2component",
    { style: styles, ...props },
    F0.dim(O
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "  ".padEnd(y
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1",
  "width": "70"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mW.default.createElement(P,{bold:!0},"Configuration Error"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mW.default.createElement(P,null,"The configuration file at ",mW.default.createElement(P,{bold:!0},A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mW.default.createElement(P,{bold:!0},"Choose an option:"
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"Reset with default configuration",value:"reset"}],onChange:(F
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press ",Z.keyName," again to exit"
  );
}


function Tc4Component(props) {
  const styles = {};
  
  return React.createElement(
    "tc4component",
    { style: styles, ...props },
    onReset:(
  );
}


function DT1Component(props) {
  const styles = {};
  
  return React.createElement(
    "dt1component",
    { style: styles, ...props },
    cT1.default.createElement(P,null,D
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    j_.default.createElement(v,{marginBottom:1,flexDirection:"column"},j_.default.createElement(P,{bold:!0},"You've spent $5 on the Anthropic API this session."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "height": "B"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yw.createElement(P,null,"  ","⎿  "
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    E31.createElement(P,{color:"error"},"No (tell Claude what to do differently
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HK.createElement(v,{minWidth:2},HK.createElement(P,{color:F,bold:D},A==="completed"?AA.checkboxOn:AA.checkboxOff," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "F"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Q
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","(P",B==="high"?"0":B==="medium"?"1":"2","
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    kw.createElement(P,{dimColor:!0},"(Empty todo list
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B.sort(Je2
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    aT.createElement(P,{color:"error"},B?Q:Q.split(`
`
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "… +",D," ",D===1?"line":"lines"," (",F0.bold("ctrl+r"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Ask Claude to create a new app or clone a repository"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Run /init to create a CLAUDE.md file with instructions for Claude"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Run /terminal-setup to set up terminal integration"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Use Claude to help with file analysis, editing, bash commands and git"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Be as specific as you would with another engineer for the best results"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    M3.createElement(P,{color:"secondaryText"},"Tips for getting started:"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.tick," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Note: You have launched ",M3.createElement(P,{bold:!0},"claude"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    W?J:G,V&&b31.default.createElement(P,{color:"secondaryText"},D.value?.endsWith(" "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J5.default.createElement(v,{flexDirection:"column",borderStyle:"round",borderColor:"ide",paddingLeft:1,paddingRight:1,gap:1},J5.default.createElement(v,null,J5.default.createElement(P,{color:"claude"},"✻ "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J5.default.createElement(P,null,"Welcome to ",J5.default.createElement(P,{bold:!0},"Claude Code"
  );
}


function PComponent(props) {
  const styles = {
  "color": "ide"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "installed ",I," v",F
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J5.default.createElement(P,{color:"warning"},AA.warning," Restart ",G," (",Y,"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J5.default.createElement(P,null,"• Claude has context of"," ",J5.default.createElement(P,{color:"suggestion"},"⧉ open files"
  );
}


function PComponent(props) {
  const styles = {
  "color": "suggestion"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "⧉ selected lines"
  );
}


function PComponent(props) {
  const styles = {
  "color": "diffAddedWord"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "+11"
  );
}


function PComponent(props) {
  const styles = {
  "color": "diffRemovedWord"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "-22"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " for Quick Launch"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","to reference files or lines in your input"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J5.default.createElement(P,{dimColor:!0},Q.pending?J5.default.createElement(J5.default.Fragment,null,"Press ",Q.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    qHB
  );
}


function PComponent(props) {
  const styles = {
  "color": "Q?error:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A.map((D,Z
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    _D.createElement(bA,{height:1},_D.createElement(P,null,"[Image]"
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    _D.createElement(bA,{height:1},_D.createElement(P,{color:"secondaryText"},"(No content
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    tJ.createElement(v,{flexDirection:"row"},tJ.createElement(P,null,"  ⎿  "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(No resources found
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    RK.createElement(bA,{height:1},RK.createElement(P,{color:"secondaryText"},"(No content
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No cells found in notebook"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No cells found in notebook"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    eJ.createElement(P,null,"Read ",eJ.createElement(P,{bold:!0},A.length
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    H8.createElement(P,null,"Read image (",Z,"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No cells found in notebook"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    H8.createElement(P,null,"Read ",H8.createElement(P,{bold:!0},D.length
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    H8.createElement(P,null,"Read PDF (",Z,"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    H8.createElement(P,null,"Read ",H8.createElement(P,{bold:!0},G
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.split(`
`
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    sZ.createElement(P,null,"Listed ",sZ.createElement(P,{bold:!0},D.split(`
`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B$.createElement(P,{color:"error"},"User rejected Claude's plan:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B$.createElement(P,{color:"secondaryText"},DX(A,B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    ZX.createElement(v,{flexDirection:"row"},ZX.createElement(P,{color:"planMode"},IM
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    DX(A,Q
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,{bold:!0,color:"permission"},"Submit Bug Report"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"Describe the issue below:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,{color:"error"},J
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press any key to close"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"This report will include:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"- Your bug description: ",Z2.createElement(P,{dimColor:!0},F
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    $A.platform,", ",$A.terminal,", v",{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"1.0.61"}.VERSION
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    V.gitState.branchName,V.gitState.commitHash?`, ${V.gitState.commitHash.slice(0,7
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,{wrap:"wrap",dimColor:!0},"We will use your feedback to debug related issues or to improve"," ",E2,"'s functionality (eg. to reduce the risk of bugs occurring in the future
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"Press ",Z2.createElement(P,{bold:!0},"Enter"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"Submitting report…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J?Z2.createElement(P,{color:"error"},J
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Thank you for your report!"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Feedback ID: ",Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,null,"Press "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Enter "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z2.createElement(P,{dimColor:!0},$.pending?Z2.createElement(Z2.Fragment,null,"Press ",$.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TP.createElement(P,null,A.trim(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    W.map((J,X
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:I,width:Q,hidden:Z}
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "D?diffAddedDimmed:diffAdded"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(P,{dimColor:D},"+","  "
  );
}


function PComponent(props) {
  const styles = {
  "color": "G?text:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    J
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:I,width:Q,hidden:Z}
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "D?diffRemovedDimmed:diffRemoved"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(P,{dimColor:D},"-","  "
  );
}


function PComponent(props) {
  const styles = {
  "color": "G?text:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    J
  );
}


function PComponent(props) {
  const styles = {
  "color": "F?text:void 0",
  "backgroundColor": "K"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",C
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:O===0?H:void 0,width:X,hidden:G}
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:O===0?H:void 0,width:X,hidden:G}
  );
}


function PComponent(props) {
  const styles = {
  "color": "F?text:void 0",
  "backgroundColor": "D?diffAddedDimmed:diffAdded"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(P,{dimColor:D},"+ "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:O===0?H:void 0,width:X,hidden:G}
  );
}


function PComponent(props) {
  const styles = {
  "color": "F?text:void 0",
  "backgroundColor": "D?diffRemovedDimmed:diffRemoved"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(P,{dimColor:D},"- "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    uB.createElement(ae,{i:O===0?H:void 0,width:X,hidden:G}
  );
}


function PComponent(props) {
  const styles = {
  "color": "F?text:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "  ",R
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A!==void 0?A.toString(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B&&hQ.createElement(P,null,"Let's get started."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hQ.createElement(P,{bold:!0},"Choose the text style that looks best with your terminal:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Q
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"Light mode",value:"light"},{label:"Dark mode (colorblind-friendly
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hQ.createElement(P,{bold:!0},"Preview"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hQ.createElement(hV,{patch:{oldStart:1,newStart:1,oldLines:3,newLines:3,lines:["function greet(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    X
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hQ.createElement(P,{dimColor:!0},Q
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hQ.createElement(P,{dimColor:!0},W.pending?hQ.createElement(hQ.Fragment,null,"Press ",W.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D3.createElement(v,{flexDirection:"column",borderStyle:"round",borderColor:"remember",paddingX:2,paddingY:1,width:"100%"},D3.createElement(v,{marginBottom:1,flexDirection:"column"},D3.createElement(P,{color:"remember",bold:!0},"Select Model"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Switch between Claude models. Applies to this session and future Claude Code sessions. For custom model names, specify with --model."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D3.createElement(vA,{defaultValue:D,focusValue:G.some((I
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D3.createElement(P,{dimColor:!0},F.pending?D3.createElement(D3.Fragment,null,"Press ",F.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rW.default.createElement(P,{bold:!0,color:"warning"},"Allow external CLAUDE.md file imports?"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Important: Only use ",E2," with files you trust. Accessing untrusted files may pose security risks"," ",rW.default.createElement($5,{url:"https://docs.anthropic.com/s/claude-code-security"}
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"No, disable external imports",value:"no"}],onChange:(D
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rW.default.createElement(P,{dimColor:!0},Q.pending?rW.default.createElement(rW.default.Fragment,null,"Press ",Q.keyName," again to exit"
  );
}


function Ix1Component(props) {
  const styles = {};
  
  return React.createElement(
    "ix1component",
    { style: styles, ...props },
    skipExitHandling:!0}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q2.createElement(v,{flexDirection:"column",minHeight:2,marginBottom:1},q2.createElement(P,{bold:!0},"Settings"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Configure ",E2," preferences"
  );
}


function VComponent(props) {
  const styles = {
  "height": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q2.createElement(v,{width:44},q2.createElement(P,{color:y?"suggestion":void 0},y?AA.pointer:" "," ",j.label
  );
}


function PComponent(props) {
  const styles = {
  "color": "y?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    j.value.toString(
  );
}


function PComponent(props) {
  const styles = {
  "color": "y?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    ((
  );
}


function PComponent(props) {
  const styles = {
  "color": "y?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    ((
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(OSC 9
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(\\a
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(OSC 99
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(OSC 777
  );
}


function PComponent(props) {
  const styles = {
  "color": "y?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    j.value.toString(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q2.createElement(P,{dimColor:!0},W.pending?q2.createElement(q2.Fragment,null,"Press ",W.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {
  "color": "permission"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press ",wZ1.createElement(P,{bold:!0},"Enter"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rZ.createElement(P,{bold:!0},"Invalid Settings"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rZ.createElement(P,null,Z
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rZ.createElement(P,{color:"secondaryText"},Y
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Array.from(I.values(
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    W.suggestion
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Learn more: ",W.docLink
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    P3.default.createElement(v,null,(D||Z
  );
}


function PComponent(props) {
  const styles = {
  "color": "D?error:warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "[",D?"Failed to parse":"Contains warnings","]"," "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Location: "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    kC(A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B.map((G,F
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "└ "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "[Error]"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",I&&`[${I}] `,G.path&&G.path!==""?`${G.path}: `:"",G.message
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "└ "
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "[Warning]"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",I&&`[${I}] `,G.path&&G.path!==""?`${G.path}: `:"",G.message
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    P3.default.createElement(P,{bold:!0},"MCP Config Diagnostics"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    P3.default.createElement(P,{color:"secondaryText"},"For help configuring MCP servers, see:"," ",P3.default.createElement($5,{url:"https://docs.anthropic.com/en/docs/claude-code/mcp"},"https://docs.anthropic.com/en/docs/claude-code/mcp"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $4.default.createElement(P,{color:"secondaryText"},"Checking installation status…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $4.default.createElement(P,{bold:!0},"Claude CLI Diagnostic"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Recommendation: ",B.recommendation.split(`
`
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B.recommendation.split(`
`
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Warning: Multiple installations found"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "- ",G.type," at ",G.path
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $4.default.createElement(P,{color:"warning"},"Warning: ",G.issue
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    zx1.default.createElement(P,{color:"text"},"※ Tip: Use git worktrees to run multiple Claude sessions in parallel."," ",zx1.default.createElement($5,{url:"https://docs.anthropic.com/s/claude-code-worktrees"},"Learn more"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    NZ1.default.createElement(P,{color:"ide"},AA.arrowUp
  );
}


function PComponent(props) {
  const styles = {
  "color": "ide"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    I,"s"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(A88,{mode:A,key:"spinnerMode"}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    eF(Math.round(W/4
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText",bold:!0},"esc"," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "to interrupt"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"error",bold:!0},"offline"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "("
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "
  );
}


function VComponent(props) {
  const styles = {
  "height": "1",
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:L},wx1[G%wx1.length]
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%",
  "alignItems": "flex-start"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(v,{flexDirection:"row",flexWrap:"wrap",marginTop:1,width:"100%"},R,d2.createElement(P,{color:L},E,"… "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "alignItems": "flex-start",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:L},"  ","⎿  "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:L},"Tip: ",K
  );
}


function VComponent(props) {
  const styles = {
  "height": "1",
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText"},"⚒"
  );
}


function VComponent(props) {
  const styles = {
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText"},AA.arrowDown
  );
}


function VComponent(props) {
  const styles = {
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText"},AA.arrowDown
  );
}


function VComponent(props) {
  const styles = {
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText"},AA.arrowUp
  );
}


function VComponent(props) {
  const styles = {
  "height": "1",
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:"secondaryText"},A?"⚒":" "
  );
}


function VComponent(props) {
  const styles = {
  "height": "1",
  "width": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d2.createElement(P,{color:Q===!1?"secondaryText":"text"},wx1[A]
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D7.createElement(v,{marginBottom:1,flexDirection:"row",justifyContent:"space-between"},D7.createElement(P,{color:"remember",bold:!0},Q||"Where should this memory be saved?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D7.createElement(vA,{focusValue:Z,options:I,onFocus:(Y
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D?D(Z
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Example project memory: “Run lint with the following command after major edits: npm run lint”"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Example local memory: “Use my sandbox URL for testing: https://myapp.local”"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Example user memory: “Don't add new comments when editing code”"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uZ1.createElement(P,{color:"text"},e_1(A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.map((G,F
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    I$.createElement(P,{color:"secondaryText"}," L "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ".repeat(Y.length+2
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " L "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d8.createElement(v,{flexDirection:"column",marginTop:1,marginBottom:1},d8.createElement(P,{bold:!0},"Memory Files"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    d8.createElement(P,{dimColor:!0},"Learn more:"," ",d8.createElement($5,{url:"https://docs.anthropic.com/en/docs/claude-code/memory"}
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Memory file does not exist yet. [Enter] to create ",I,"."
  );
}


function PComponent(props) {
  const styles = {
  "color": "remember"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G," ",G===1?"memory":"memories"," in"," ",Qv1(B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,{bold:!0,color:"claude"},`${E2} v${{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"1.0.61"}.VERSION}`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,null,"Always review Claude's responses, especially when running code. Claude has read access to files in the current directory and can run commands and edit files with your permission."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,{bold:!0},"Usage Modes:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "claude"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    'claude -p "question"'
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,null,"Run ",y2.createElement(P,{bold:!0},"claude -h"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,{bold:!0},"Common Tasks:"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "> How does foo.py work?"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "> Update bar.ts to..."
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "> cargo build"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "> /help"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "> !ls"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,{bold:!0},"Interactive Mode Commands:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.map((I,Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,null,y2.createElement(P,{bold:!0},`/${I.name}`
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y2.createElement(P,{color:"secondaryText"},Q
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F.pending?y2.createElement(P,{dimColor:!0},"Press ",F.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VX.default.createElement(v,{flexDirection:"column",borderStyle:"round",borderColor:"ide",paddingX:2,paddingY:1,width:"100%"},VX.default.createElement(v,{marginBottom:1},VX.default.createElement(P,{color:"ide"},"Do you wish to enable auto-connect to IDE?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VX.default.createElement(vA,{options:[{label:"Yes",value:"yes"},{label:"No",value:"no"}],onChange:Q,defaultValue:"yes",onCancel:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VX.default.createElement(P,{dimColor:!0},"You can also configure this in /config or with the --ide flag"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VX.default.createElement(P,{dimColor:!0},B.pending?VX.default.createElement(VX.default.Fragment,null,"Press ",B.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(v,{flexDirection:"column",borderStyle:"round",borderColor:"ide",paddingX:2,paddingY:1,width:"100%"},FQ.default.createElement(v,{flexDirection:"column"},FQ.default.createElement(P,{color:"ide",bold:!0},"Select IDE"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Connect to an IDE for integrated development features."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},iV0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(vA,{defaultValue:F,focusValue:F,options:V,onFocus:(C
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},"※ Tip: You can enable auto-connect to IDE in /config or with the --ide flag"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},"Found ",B.length," other running IDE(s
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B.map((C,K
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},"• ",C.name,": ",C.workspaceFolders.join(", "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},G.pending?FQ.default.createElement(FQ.default.Fragment,null,"Press ",G.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(v,{marginBottom:1},FQ.default.createElement(P,{color:"ide"},"Select IDE to install extension:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(vA,{focusValue:Z,options:I,onFocus:(Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FQ.default.createElement(P,{dimColor:!0},D.pending?FQ.default.createElement(FQ.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "alignItems": "flex-start"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    M$0.default.createElement(P,{color:"claude"},` ██████╗██╗      █████╗ ██╗   ██╗██████╗ ███████╗
██╔════╝██║     ██╔══██╗██║   ██║██╔══██╗██╔════╝
██║     ██║     ███████║██║   ██║██║  ██║█████╗  
██║     ██║     ██╔══██║██║   ██║██║  ██║██╔══╝  
╚██████╗███████╗██║  ██║╚██████╔╝██████╔╝███████╗
 ╚═════╝╚══════╝╚═╝  ╚═╝ ╚═════╝ ╚═════╝ ╚══════╝
 ██████╗ ██████╗ ██████╗ ███████╗                
██╔════╝██╔═══██╗██╔══██╗██╔════╝                
██║     ██║   ██║██║  ██║█████╗                  
██║     ██║   ██║██║  ██║██╔══╝                  
╚██████╗╚██████╔╝██████╔╝███████╗                
 ╚═════╝ ╚═════╝ ╚═════╝ ╚══════╝`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oW.default.createElement(P,{bold:!0,color:"warning"},"Detected a custom API key in your environment"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "ANTHROPIC_API_KEY"
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:`No (${F0.bold("recommended"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oW.default.createElement(P,{dimColor:!0},D.pending?oW.default.createElement(oW.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D&&G?HI.default.createElement(v,{paddingLeft:1},HI.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HI.default.createElement(P,{color:"error"},"Unable to connect to Anthropic services"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B?.error
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    HI.default.createElement(P,null,"Please check your internet connection and network settings."
  );
}


function PComponent(props) {
  const styles = {
  "color": "suggestion"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "https://anthropic.com/supported-countries"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    OQ.default.createElement(P,{bold:!0},"Security notes:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "70"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    OQ.default.createElement(A_,null,OQ.default.createElement(A_.Item,null,OQ.default.createElement(P,null,"Claude can make mistakes"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "You should always review Claude's responses, especially when",OQ.default.createElement(H3,null
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "For more details see:",OQ.default.createElement(H3,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    OQ.default.createElement(P,{bold:!0},"Use ",E2,"'s terminal setup?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "70"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    OQ.default.createElement(P,null,"For the optimal coding experience, enable the recommended settings",OQ.default.createElement(H3,null
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"No, maybe later with /terminal-setup",value:"no"}],onChange:(K
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Y.pending?OQ.default.createElement(OQ.default.Fragment,null,"Press ",Y.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    C[B]?.id!=="oauth"&&OQ.default.createElement(R$0,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "0"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    C[B]?.component,Y.pending&&OQ.default.createElement(v,{padding:1},OQ.default.createElement(P,{dimColor:!0},"Press ",Y.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✻"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    E2
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wF.createElement(P,{color:"text"},"Your organization has enrolled in the"," ",wF.createElement($5,{url:"https://support.anthropic.com/en/articles/********-about-the-development-partner-program"},"Development Partner Program"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wF.createElement(P,{color:"text"},"Enrolled in"," ",wF.createElement($5,{url:"https://support.anthropic.com/en/articles/********-about-the-development-partner-program"},"Development Partner Program"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{bold:!0},B?B:`${E2} can now be used with your Claude subscription or billed based on API usage through your Console account.`
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{bold:!0},"Select login method:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F&&BB.default.createElement(v,null,BB.default.createElement(P,{dimColor:!0},F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(v,null,BB.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{color:"permission"},"Retrying…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q==="setup-token"&&I.token?null:BB.default.createElement(BB.default.Fragment,null,E0(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Logged in as"," ",BB.default.createElement(P,null,E0(
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Login successful. Press ",BB.default.createElement(P,{bold:!0},"Enter"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{color:"error"},"OAuth error: ",I.message
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{color:"permission"},"Press ",BB.default.createElement(P,{bold:!0},"Enter"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(R$0,null
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(uMB,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(v,{paddingX:1},BB.default.createElement(P,{dimColor:!0},"Browser didn't open? Use the url below to sign in:"
  );
}


function VComponent(props) {
  const styles = {
  "width": "1000"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{dimColor:!0},I.url
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{color:"success"},"✓ Long-lived authentication token created successfully!"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,null,"Your OAuth token (valid for 1 year
  );
}


function VComponent(props) {
  const styles = {
  "width": "1000"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(P,{color:"warning"},I.token
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Store this token securely. You won't be able to see it again."
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Use this token by setting: export CLAUDE_CODE_OAUTH_TOKEN=<token>"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    BB.default.createElement(v,{paddingLeft:1,flexDirection:"column",gap:1},j(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    EQ.createElement(v,{...Z?{}:{borderColor:"claude",borderStyle:"round"},flexDirection:"column",gap:1,paddingLeft:1,width:B},EQ.createElement(P,null,EQ.createElement(P,{color:"claude"},"✻"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    E2
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    EQ.createElement(P,{color:"secondaryText",italic:!0},"/help for help, /status for your current setup"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "cwd: ",a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    EQ.createElement(v,{marginBottom:1},EQ.createElement(P,{color:"secondaryText"},"Overrides (via env
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• API Key:"," ",EQ.createElement(P,{bold:!0},Q.length<25?`${Q.slice(0,3
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• Prompt caching:"," ",EQ.createElement(P,{color:"error",bold:!0},"off"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• API timeout:"," ",EQ.createElement(P,{bold:!0},process.env.API_TIMEOUT_MS,"ms"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• Max thinking tokens:"," ",EQ.createElement(P,{bold:!0},process.env.MAX_THINKING_TOKENS
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• API Base URL:"," ",EQ.createElement(P,{bold:!0},process.env.ANTHROPIC_BASE_URL
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    UI.createElement(Mm,{onDone:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    UI.createElement(P,{dimColor:!0},Q.pending?UI.createElement(UI.Fragment,null,"Press ",Q.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uY.default.createElement(v,{flexDirection:"column",marginBottom:1},uY.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Select GitHub repository"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uY.default.createElement(P,{bold:B,color:B?"permission":void 0},B?"> ":"  ","Use current repository: ",A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uY.default.createElement(P,{bold:!B||!A,color:!B||!A?"permission":void 0},!B||!A?"> ":"  ","Enter a different repository"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uY.default.createElement(t6,{value:Q,onChange:D,onSubmit:Z,focus:!0,placeholder:"owner/repo or https://github.com/owner/repo",columns:W,cursorOffset:F,onChangeCursorOffset:I,showCursor:!0}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uY.default.createElement(P,{dimColor:!0},A?"↑/↓ to select · ":"","Enter to continue"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(v,{flexDirection:"column",marginBottom:1},gG.default.createElement(P,{bold:!0},"Install the Claude GitHub App"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,null,"Opening browser to install the Claude GitHub App…"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,null,"If your browser doesn't open automatically, visit:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,{underline:!0},"https://github.com/apps/claude"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,null,"Please install the app for repository: ",gG.default.createElement(P,{bold:!0},A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,{dimColor:!0},"Important: Make sure to grant access to this specific repository"
  );
}


function PComponent(props) {
  const styles = {
  "color": "permission"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press Enter once you've installed the app",AA.ellipsis
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    gG.default.createElement(P,{dimColor:!0},"Having trouble? See manual setup instructions at:"," ",gG.default.createElement(P,{color:"claude"},"https://github.com/anthropics/claude-code-action/#manual-setup-direct-api"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(v,{flexDirection:"column",marginBottom:1},VD.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Setup API key secret"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,{color:"warning"},"ANTHROPIC_API_KEY already exists in repository secrets!"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,null,"Would you like to:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,null,A?lB("success",Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,null,!A?lB("success",Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,null,"Enter new secret name (alphanumeric with underscores
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    VD.default.createElement(P,{dimColor:!0},"↑/↓ to select · Enter to continue"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wI.default.createElement(v,{flexDirection:"column",marginBottom:1},wI.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Choose API key"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wI.default.createElement(P,null,F==="existing"?lB("success",X
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wI.default.createElement(P,null,F==="oauth"?lB("success",X
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wI.default.createElement(P,null,F==="new"?lB("success",X
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    wI.default.createElement(P,{dimColor:!0},"↑/↓ to select · Enter to continue"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bP.default.createElement(v,{flexDirection:"column",marginBottom:1},bP.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Create GitHub Actions workflow"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bP.default.createElement(P,{color:W==="completed"?"success":W==="in-progress"?"warning":void 0},W==="completed"?"✓ ":"",I,W==="in-progress"?"…":""
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z7.default.createElement(v,{flexDirection:"column",marginBottom:1},Z7.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Success"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✓ GitHub Actions workflow created!"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z7.default.createElement(P,{color:"success"},"✓ Using existing ANTHROPIC_API_KEY secret"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z7.default.createElement(P,{color:"success"},"✓ API key saved as ",Q," secret"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z7.default.createElement(P,null,"Next steps:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z7.default.createElement(P,{dimColor:!0},"Press any key to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(v,{flexDirection:"column",marginBottom:1},uG.default.createElement(P,{bold:!0},"Install GitHub App"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Error"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Error: ",A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(P,{dimColor:!0},"Reason: ",B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(P,{dimColor:!0},"How to fix:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(P,{dimColor:!0},"• "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(P,{dimColor:!0},"For manual setup instructions, see:"," ",uG.default.createElement(P,{color:"claude"},"https://github.com/anthropics/claude-code-action/#manual-setup-direct-api"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    uG.default.createElement(P,{dimColor:!0},"Press any key to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    pV.default.createElement(v,{flexDirection:"column",marginBottom:1},pV.default.createElement(P,{bold:!0},"Existing Workflow Found"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Repository: ",A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    pV.default.createElement(P,null,"A Claude workflow file already exists at"," ",pV.default.createElement(P,{color:"claude"},".github/workflows/claude.yml"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "What would you like to do?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    pV.default.createElement(vA,{options:[{label:"Update workflow file with latest version",value:"update"},{label:"Skip workflow update (configure secrets only
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    pV.default.createElement(P,{dimColor:!0},"View the latest workflow template at:"," ",pV.default.createElement(P,{color:"claude"},"https://github.com/anthropics/claude-code-action/blob/main/examples/claude.yml"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mY.default.createElement(v,{flexDirection:"column",marginBottom:1},mY.default.createElement(P,{bold:!0},AA.warning," Setup Warnings"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "We found some potential issues, but you can continue anyway"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mY.default.createElement(P,{color:"warning",bold:!0},Q.title
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q.instructions.map((Z,G
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• ",Z
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mY.default.createElement(P,{bold:!0,color:"permission"},"Press Enter to continue anyway, or Ctrl+C to exit and fix issues"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mY.default.createElement(P,{dimColor:!0},"You can also try the manual setup steps if needed:"," ",mY.default.createElement(P,{color:"claude"},"https://github.com/anthropics/claude-code-action/#manual-setup-direct-api"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FZ.default.createElement(v,{flexDirection:"column",marginBottom:1},FZ.default.createElement(P,{bold:!0},"Select GitHub workflows to install"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "We'll create a workflow file in your repository for each one you select."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y.map((W,J
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FZ.default.createElement(v,{marginRight:1,minWidth:2},FZ.default.createElement(P,{bold:V},X?"✓":" "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FZ.default.createElement(P,{bold:V},W.label
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    W.description
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FZ.default.createElement(P,{dimColor:!0},"↑↓ Navigate · Space to toggle · Enter to confirm"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    FZ.default.createElement(P,{color:"error"},"You must select at least one workflow to continue"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(P,{bold:!0},"Create Authentication Token"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Creating a long-lived token for GitHub Actions"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(v,{paddingX:1},c9.default.createElement(P,{dimColor:!0},"Browser didn't open? Use the url below to sign in:"
  );
}


function VComponent(props) {
  const styles = {
  "width": "1000"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(P,{dimColor:!0},Q.url
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    !W&&c9.default.createElement(v,null,c9.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(P,{color:"success"},"✓ Authentication token created successfully!"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Using token for GitHub Actions setup…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(P,{color:"error"},"OAuth error: ",Q.message
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press Enter to try again, or any other key to cancel"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press any key to return to API key selection"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    c9.default.createElement(P,{color:"permission"},"Retrying…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q.state==="starting"&&c9.default.createElement(v,{flexDirection:"column",gap:1,paddingBottom:1},c9.default.createElement(P,{bold:!0},"Create Authentication Token"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Creating a long-lived token for GitHub Actions"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Enter"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Esc"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0},E2," Local Installer"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{color:"secondaryText"},`This will install ${E2} to ~/.claude/local`
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "instead of using a global npm installation."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0},"Installing ",E2," locally..."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0,color:"success"},"✓ Local installation successful!"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,"Next, let's add an alias for `claude`"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0},"Setting up alias for claude..."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0},"Alias setup complete"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,Z
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,"Next, we'll remove the globally installed npm package"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0},"Uninstalling global ",E2,"..."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0,color:"success"},"✓ Global installation removed successfully!"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,E2," is now installed locally."
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F0.bold("claude"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0,color:"warning"},"! Could not remove global installation"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,"The local installation is installed, but we couldn't remove the global npm package automatically."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,"You can remove it manually later with:",`
`,F0.bold(`npm uninstall -g --force ${{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"1.0.61"}.PACKAGE_URL}`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,{bold:!0,color:"error"},"✗ Installation failed"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TB.default.createElement(P,null,Q||"An unexpected error occurred during installation."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(Kx1,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(v,{marginBottom:1},CD.default.createElement(P,{bold:!0},"Manage MCP servers"
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    onCancel:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},"※ Tip:"," ",G?`Error logs will be shown inline. Log files are also saved in
  ${YN.baseLogs(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},"MCP Config locations (by scope
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},"• ",hf(Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},"• ",kC(Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},"For help configuring MCP servers, see:"," ",CD.default.createElement($5,{url:"https://docs.anthropic.com/en/docs/claude-code/mcp"},"https://docs.anthropic.com/en/docs/claude-code/mcp"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CD.default.createElement(P,{dimColor:!0},Z.pending?CD.default.createElement(CD.default.Fragment,null,"Press ",Z.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Capabilities: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D.length>0?D.join(" · "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w8.default.createElement(v,{marginBottom:1},w8.default.createElement(P,{bold:!0},I," MCP Server"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w8.default.createElement(v,null,w8.default.createElement(P,{bold:!0},"Status: "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Command: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.config.command
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Args: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.config.args.join(" "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Config location: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    kC(wi(A.name
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Tools: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B," tools"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w8.default.createElement(vA,{options:W,onChange:(J
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w8.default.createElement(P,{dimColor:!0},G.pending?w8.default.createElement(w8.default.Fragment,null,"Press ",G.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(P,{color:"claude"},"Authenticating with ",A.name,"…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(P,{dimColor:!0},"If your browser doesn't open automatically, copy this URL manually:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Return here after authenticating in your browser."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(v,{marginBottom:1},i9.default.createElement(P,{bold:!0},E," MCP Server"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(v,null,i9.default.createElement(P,{bold:!0},"Status: "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "URL: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.config.url
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Config location: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    kC(wi(A.name
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Tools: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B," tools"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(P,{color:"error"},"Error: ",W
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(vA,{options:T,onChange:async(R
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    i9.default.createElement(P,{dimColor:!0},F.pending?i9.default.createElement(i9.default.Fragment,null,"Press ",F.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CX.default.createElement(v,{flexDirection:"column",paddingX:1,borderStyle:"round"},CX.default.createElement(v,{marginBottom:1},CX.default.createElement(P,{bold:!0},"Tools for ",A.name
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " (",G.length," tools
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No tools available"
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    onCancel:Q}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    CX.default.createElement(P,{dimColor:!0},D.pending?CX.default.createElement(CX.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B8.default.createElement(v,{flexDirection:"column",paddingX:1,borderStyle:"round"},B8.default.createElement(v,{marginBottom:1},B8.default.createElement(P,{bold:!0},Y,B8.default.createElement(P,{color:"secondaryText"}," (",B.name,"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " [read-only]"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " [destructive]"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " [open-world]"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B8.default.createElement(v,null,B8.default.createElement(P,{bold:!0},"Tool name: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Full name: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.name
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B8.default.createElement(P,{bold:!0},"Description:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B8.default.createElement(P,{bold:!0},"Parameters:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Object.entries(A.inputJSONSchema.properties
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• ",V,E&&B8.default.createElement(P,{color:"secondaryText"}," (required
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    typeof C==="object"&&C&&"type"in C?String(C.type
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","- ",String(C.description
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B8.default.createElement(P,{dimColor:!0},D.pending?B8.default.createElement(B8.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "height": "B-1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    KX.default.createElement(v,{paddingLeft:3+V},KX.default.createElement(P,{bold:!0,color:"text"},"Modified"
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Created"
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "# Messages"
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Git Branch"
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Summary"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    KX.default.createElement(P,{color:"secondaryText"},"and ",F," more…"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    eW.default.createElement(P,{color:"secondaryText"},"[Image data detected and sent to Claude]"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eW.default.createElement(yV,{content:Y,verbose:!1}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eW.default.createElement(yV,{content:Y,verbose:B}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eW.default.createElement(P,{bold:!0},"=== Original Output ==="
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q!==""?eW.default.createElement(yV,{content:Q,verbose:B}
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    eW.default.createElement(P,{color:"secondaryText"},I?"Running in the background (down arrow to manage
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    k11.default.createElement(v,{height:5,flexDirection:"column",overflow:"hidden"},k11.default.createElement(P,{color:"secondaryText"},G
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F>0&&`+${F} more line${F===1?"":"s"}`,I
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q7.createElement(P,{dimColor:!0},"ctrl+b to run in background"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    q7.createElement(P,{color:"secondaryText"},"Running…"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    q7.createElement(P,{color:"secondaryText"},"Waiting…"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.tick," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.warning," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " L "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    jQ.createElement(v,{flexDirection:"column",gap:1},jQ.createElement(v,null,jQ.createElement(P,{bold:!0},"Claude Code Status "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "v",B
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " L "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    jQ.createElement(v,null,jQ.createElement(P,{bold:!0},Y.title," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• ",Y.command
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.pending?jQ.createElement(P,{dimColor:!0},"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "width": "100%",
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    k6.default.createElement(v,{width:"100%"},k6.default.createElement(v,{borderStyle:"round",borderColor:"permission",flexDirection:"column",padding:1,width:"100%"},k6.default.createElement(v,null,k6.default.createElement(P,{color:"permission",bold:!0},"Bash Details"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    k6.default.createElement(P,null,k6.default.createElement(P,{bold:!0},"ID:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Status:"
  );
}


function PComponent(props) {
  const styles = {
  "color": "permission"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.status,A.result?.code!==void 0&&` (exit code: ${A.result.code}
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.status,A.result?.code!==void 0&&` (exit code: ${A.result.code}
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.status,A.result?.code!==void 0&&` (exit code: ${A.result.code}
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Runtime:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    k6.default.createElement(P,{bold:!0},"Command:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    k6.default.createElement(P,{bold:!0},"STDOUT:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "height": "7"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G.stdout.split(`
`
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    J
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G.stdoutLines>5?`Showing last 5 lines of ${G.stdoutLines} total lines`:`Showing ${G.stdoutLines} lines`
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No stdout output available"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    k6.default.createElement(P,{bold:!0,color:"error"},"STDERR:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "height": "3"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G.stderr.split(`
`
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    J
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G.stderrLines>1?`Showing last line of ${G.stderrLines} total lines`:`Showing ${G.stderrLines} line`
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press ",I.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press esc to close",A.status==="running"&&Q?k6.default.createElement(P,null," · k to kill shell"
  );
}


function VComponent(props) {
  const styles = {
  "width": "100%",
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    cY.default.createElement(v,{borderStyle:"round",borderColor:"permission",flexDirection:"column",padding:1,width:"100%"},cY.default.createElement(v,null,cY.default.createElement(P,{color:"permission",bold:!0},"Background Bash Shells"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    cY.default.createElement(P,null,"No background shells currently running"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Select a shell to view details:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    cY.default.createElement(vA,{options:Y,onChange:G,onCancel:A}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    I.pending?cY.default.createElement(P,{dimColor:!0},"Press ",I.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press esc to close"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Any Bash command starting with"," ",EX.createElement(P,{bold:!0},A.ruleContent.slice(0,-2
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "The Bash command ",EX.createElement(P,{bold:!0},A.ruleContent
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Any Bash command"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Any use of the ",EX.createElement(P,{bold:!0},A.toolName
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J$.default.createElement(P,{bold:!0},A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J$.default.createElement(P,{dimColor:!0},Z.pending?J$.default.createElement(J$.default.Fragment,null,"Press ",Z.keyName," again to exit"
  );
}


function Lv1Component(props) {
  const styles = {};
  
  return React.createElement(
    "lv1component",
    { style: styles, ...props },
    lY.createElement(v,{flexDirection:"column",paddingX:2},Q.map((W
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    lY.createElement(P,{bold:!0},j5(W
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    lY.createElement(P,null,Q.length===1?"Where should this rule be saved?":"Where should these rules be saved?"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    aV.default.createElement(P,{color:"secondaryText"},"Fetching…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    aV.default.createElement(bA,{height:1},aV.default.createElement(P,null,"Received ",aV.default.createElement(P,{bold:!0},F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    aV.default.createElement(P,null,D
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    aV.default.createElement(P,null,"Received ",aV.default.createElement(P,{bold:!0},F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l8.createElement(P,{bold:!0,color:"permission"},"Add ",Q," permission rule"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l8.createElement(P,null,"Permission rules are a tool name, optionally followed by a specifier in parentheses.",l8.createElement(H3,null
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    j5({toolName:ZJ.name}
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " or "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    j5({toolName:TQ.name,ruleContent:"ls:*"}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l8.createElement(t6,{showCursor:!0,value:D,onChange:Z,onSubmit:Y,placeholder:`Enter permission rule${AA.ellipsis}`,columns:I,cursorOffset:D.length,onChangeCursorOffset:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G.pending?l8.createElement(P,{dimColor:!0},"Press ",G.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Enter to submit · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    sV.createElement(v,{flexDirection:"row",marginTop:1,marginLeft:2,gap:1},sV.createElement(P,null,`-  ${P9(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(Original working directory
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    um.default.createElement(P,{bold:!0,color:"permission"},"Permissions:"
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "A===Q?permission:void 0",
  "color": "A===Q?inverseText:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    ` ${KJ8(Q
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    R5.createElement(P,{bold:!0,color:"permission"},"Add directory to workspace"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    R5.createElement(P,null,E2," will be able to read files in this directory and make edits when auto-accept edits is on."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    R5.createElement(P,null,"Enter the path to the directory:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    R5.createElement(t6,{showCursor:!0,placeholder:`Directory path${AA.ellipsis}`,value:Z,onChange:G,onSubmit:W,columns:80,cursorOffset:Z.length,onChangeCursorOffset:(
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y.pending?R5.createElement(P,{dimColor:!0},"Press ",Y.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Enter to add · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G7.createElement(P,{bold:!0,color:"error"},"Remove directory from workspace?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G7.createElement(P,{bold:!0},A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G7.createElement(vA,{onChange:I,onCancel:Q,options:[{label:"Yes",value:"yes"},{label:"No",value:"no"}]}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G.pending?G7.createElement(P,{dimColor:!0},"Press ",G.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "↑/↓ to select · Enter to confirm · Esc to cancel"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    `From ${Fi(A.source
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    DB.createElement(P,{bold:!0},j5(A.ruleValue
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.pending?DB.createElement(P,{dimColor:!0},"Press ",D.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    DB.createElement(P,{bold:!0,color:"permission"},"Rule details"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "This rule is configured by managed settings and cannot be modified.",`
`,"Contact your system administrator for more information."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    DB.createElement(P,{bold:!0,color:"error"},"Delete ",zJ8(A.ruleBehavior
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"No",value:"no"}]}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    DB.createElement(vA,{options:j,onChange:y,onCancel:(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    DB.createElement(IkB,{selectedTab:Y}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    b.pending?DB.createElement(P,{dimColor:!0},"Press ",b.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Tab to select tab · Enter to confirm · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q8.createElement(v,{flexDirection:"row",gap:1},q8.createElement(xD,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q8.createElement(P,{bold:!0,color:"error"},"Failed to add hook"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q8.createElement(P,{bold:!0,color:"success"},"Save hook configuration"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q8.createElement(P,null,"Event: ",A," - ",B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(v,{flexDirection:"column",marginBottom:1},f2.createElement(v,null,f2.createElement(P,{bold:!0,color:"warning"},"Hook Configuration"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(v,{marginY:0.5},f2.createElement(P,null,F0.bold("Hooks"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(P,null,"• Each hook event has its own input and output behavior"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(v,{flexDirection:"column"},f2.createElement(P,{bold:!0,color:"error"},AA.warning," CRITICAL SECURITY WARNING - USE AT YOUR OWN RISK"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(v,{flexDirection:"column"},f2.createElement(P,{bold:!0,color:"warning"},AA.warning," Settings Changed"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    f2.createElement(P,{bold:!0},"Select hook event:"
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    onCancel:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B?f2.createElement(P,{dimColor:!0},"Press ",Q," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Enter to acknowledge risks and continue · Esc to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z3.createElement(P,{bold:!0,color:"suggestion"},A," - Tool Matchers"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z3.createElement(P,{dimColor:!0},D
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z3.createElement(vA,{options:[{label:`+ Add new matcher${AA.ellipsis}`,value:"add-new"},...F.map((I
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z3.createElement(P,{dimColor:!0},"No matchers configured yet"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z3.createElement(P,{dimColor:!0},"Enter to select · Esc to go back"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,{bold:!0,color:"success"},"Add new matcher for ",A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,{dimColor:!0},D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,null,"Possible matcher values for field ",Z.fieldToMatch,":"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z.values.join(", "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,null,"Tool matcher:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(t6,{value:B,onChange:Q,columns:78,showCursor:!0,cursorOffset:G,onChangeCursorOffset:F}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,{dimColor:!0},"Example Matchers:",`
`,"• Write (single tool
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    n4.createElement(P,{dimColor:!0},"Enter to confirm · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q2.createElement(P,{bold:!0,color:"success"},"Add new hook"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q2.createElement(P,{bold:!0,color:"error"},AA.warning," CRITICAL SECURITY WARNING"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q2.createElement(t6,{value:G,onChange:F,columns:W-8,showCursor:!0,cursorOffset:I,onChangeCursorOffset:Y,multiline:!0}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    X&&Q2.createElement(P,{color:"warning"},AA.warning," Warning: Using a relative path for the executable may be insecure. Consider using an absolute path instead."
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.warning," Warning: Using sudo in hooks can be dangerous and may expose your system to security risks."
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Examples:",Q2.createElement(H3,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q2.createElement(P,{bold:!0,color:"warning"},AA.warning," Security Best Practices:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• Use absolute paths for custom scripts (~/scripts/check.sh not check.sh
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "By adding this hook, you accept all responsibility for its execution and any consequences."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q2.createElement(P,{dimColor:!0},"Enter to confirm · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    ED.createElement(P,{bold:!0,color:"error"},"Delete matcher?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    ED.createElement(P,{bold:!0},A
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Event: ",B
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"No",value:"no"}]}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    ED.createElement(P,{dimColor:!0},"Enter to confirm · Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F7.createElement(P,{bold:!0,color:"success"},A,D.matcherMetadata!==void 0?` - Matcher: ${B}`:""
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F7.createElement(P,{dimColor:!0},D.description
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F7.createElement(vA,{options:[{label:`+ Add new hook${AA.ellipsis}`,value:"add-new"},...Q.map((F,I
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F7.createElement(P,{dimColor:!0},"No hooks configured yet"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F7.createElement(P,{dimColor:!0},"Enter to select · Esc to go back"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G3.createElement(P,{bold:!0,color:"error"},"Delete hook?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G3.createElement(P,{bold:!0},A.config.command
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Event: ",A.event
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Matcher: ",A.matcher
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    EkB(A.source
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"No",value:"no"}]}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G3.createElement(P,{dimColor:!0},"Enter to confirm · Esc to cancel"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D?A:PJ8(a0(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    N4.createElement(P,null,J
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    N4.createElement(hV,{patch:X,dim:!1,width:Z-12}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "..."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    N4.createElement(P,null,N4.createElement(P,{bold:!0},"Tip:"
  );
}


function PComponent(props) {
  const styles = {
  "color": "remember"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "# to memorize"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yQ.createElement(P,{color:"error"},"User rejected ",J==="update"?"update":"write"," to"," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z?A:eN0(a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V,gV(X.map((C
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yQ.createElement(hV,{patch:C,dim:!0,width:Q-12}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "..."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yQ.createElement(P,null,"  ","⎿ (No changes
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Y
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F?A:eN0(a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    J,yQ.createElement(v,{flexDirection:"column"},yQ.createElement(xV,{code:F?I:I.split(`
`
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "… +",W," ",W===1?"line":"lines"," ",Y>0&&yQ.createElement(Ux,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $F.createElement(P,{color:"error"},"User rejected ",B," to "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z?A:hJ8(a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    F,gV(Q.map((I
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $F.createElement(hV,{patch:I,dim:!0,width:G-12}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "..."
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    E$.createElement(P,null,"(No changes
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    qF.createElement(v,{flexDirection:"row"},qF.createElement(P,{color:"error"},"User rejected ",F," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G?A:cJ8(a0(
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " at cell ",B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    qF.createElement(P,{dimColor:!0},qF.createElement(xV,{code:Q,language:D==="markdown"?"markdown":"python"}
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    AG.createElement(P,null,"Updated cell ",AG.createElement(P,{bold:!0},A
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    AG.createElement(xV,{code:B,language:Q}
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    mm.createElement(P,null,"(No changes
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A," "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Q," "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    cG.default.createElement(v,{flexDirection:"row"},cG.default.createElement(P,null,"  ⎿  ",F,I
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    cG.default.createElement(P,null,Z
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    cG.default.createElement(P,null,F,I," ",A>0&&cG.default.createElement(Ux,null
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    hG1.createElement(P,{color:"error"},"Interrupted by user"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Interrupted by user"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    cP.createElement(X01,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "width": "F"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.renderToolResultMessage(A.toolUseResult,nr(B
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    db1.default.createElement(P,{color:B?"secondaryText":A?"error":"success"},D?IM:"  "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    GJ.default.createElement(v,{flexDirection:"column"},GJ.default.createElement(v,{flexDirection:"row",flexWrap:"nowrap",minWidth:H.length+(W?2:0
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    GJ.default.createElement(P,{color:K},IM
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    GJ.default.createElement(P,{bold:!0,wrap:"truncate-end",backgroundColor:$},H
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    GJ.default.createElement(P,null,"(",N,"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Running hook ",GJ.default.createElement(P,{bold:!0},B.data.hookName
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " • /upgrade to increase your usage limit."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},"Claude usage limit reached.",G?` Your limit will reset at ${F}.`:""
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},"Context low · Run /compact to compact & continue"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},"Credit balance too low · Add funds: https://console.anthropic.com/settings/billing"
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},g_1
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},u_1
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},m_1
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    h5.default.createElement(P,{color:"error"},"We are experiencing high demand for Opus 4."
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    h5.default.createElement(X01,null
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A===sW?`${sW}: Please wait a moment and try again.`:A
  );
}


function VComponent(props) {
  const styles = {
  "alignItems": "flex-start",
  "flexDirection": "row",
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    h5.default.createElement(v,{flexDirection:"row"},Q&&h5.default.createElement(v,{minWidth:2},h5.default.createElement(P,{color:"text"},IM
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "D-6"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    h5.default.createElement(P,null,DX(A,Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    pP.createElement(v,null,pP.createElement(P,{color:"bashBorder"},"!"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",Q
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mG1.createElement(P,{color:"secondaryText"},"> /",Q," ",D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V01.default.createElement(v,{minWidth:2,width:2},V01.default.createElement(P,{color:"secondaryText"},">"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "Q-4"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V01.default.createElement(P,{color:"secondaryText",wrap:"wrap"},B.trim(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yK.createElement(v,null,yK.createElement(P,{color:"remember"},"#"
  );
}


function PComponent(props) {
  const styles = {
  "color": "remember"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",Q
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    yK.createElement(P,{dimColor:!0},kX8(
  );
}


function SmComponent(props) {
  const styles = {};
  
  return React.createElement(
    "smcomponent",
    { style: styles, ...props },
    verbose:!!B}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    bV
  );
}


function BAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    kK.createElement(P,{color:"text"},B.trim(
  );
}


function BAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    kK.createElement(P,{color:"error"},Q.trim(
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    sY.createElement(X01,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    dG1.default.createElement(P,{color:"secondaryText",italic:!0},"✻ Thinking…"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    dG1.default.createElement(P,{color:"secondaryText",italic:!0},DX(A,Q
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    zL0.default.createElement(P,{color:"secondaryText",italic:!0},"✻ Thinking…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A.files.map((Z,G
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F0.bold(_X8(a0(
  );
}


function BAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    PM.default.createElement(P,{color:"secondaryText",wrap:"wrap"},"  ",rw.getSeveritySymbol(F.severity
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    `Found ${F0.bold(Q
  );
}


function Lb1Component(props) {
  const styles = {};
  
  return React.createElement(
    "lb1component",
    { style: styles, ...props },
    verbose:Q}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    A.trim(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    iP.createElement(v,{flexDirection:"column",width:D-10},A.level==="warning"?iP.createElement(P,{color:"warning",wrap:"wrap"},Z.trim(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z.trim(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A.message.content.map((C,K
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A.message.content.map((C,K
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    dm.createElement(cb1,{addMargin:!1,param:{text:`<bash-input>${A}</bash-input>`,type:"text"}}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    I?G.map((C
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    U9.createElement(SM,{message:C.data.message,messages:C.data.normalizedMessages,addMargin:!1,tools:F,verbose:I,erroredToolUseIDs:new Set,inProgressToolUseIDs:new Set,resolvedToolUseIDs:new Set,progressMessagesForMessage:G,shouldAnimate:!1,shouldShowDot:!1}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    U9.createElement(P,{color:"success",bold:!0},"Agent Response:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    U9.createElement(P,null,DX(C.text,Y
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    U9.createElement(SM,{message:V,messages:ZZ([V]
  );
}


function BAComponent(props) {
  const styles = {
  "height": "1"
};
  
  return React.createElement(
    "bacomponent",
    { style: styles, ...props },
    U9.createElement(P,{color:"secondaryText"},D.parallelTasksCount>1?`Initializing ${D.parallelTasksCount} parallel agents…`:"Initializing…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    U9.createElement(P,{color:"success",bold:!0},W,G&&W==="Synthesis"?" (combining results
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    U9.createElement(P,{color:"secondaryText"},I," total tool uses across ",F.size," agents"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y.map((X
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "+",J," more tool ",J===1?"use":"uses"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Searching: ",Q.query
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Found ",Q.resultCount,' results for "',Q.query,'"'
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "space-between",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    jM.default.createElement(bA,{height:1},jM.default.createElement(P,null,"Did ",B," search",B!==1?"es":""," in ",Q
  );
}


function VComponent(props) {
  const styles = {
  "width": "A"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    borderColor:Q,flexGrow:1,borderBottom:!0,borderTop:!1,borderLeft:!1,borderRight:!1,...D}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y
  );
}


function VComponent(props) {
  const styles = {
  "width": "B"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y,pm.default.createElement(v,null,pm.default.createElement(P,{color:Z},A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    q$.createElement(v,{flexDirection:"column",paddingX:1},q$.createElement(P,{bold:!0,color:B},A
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D
  );
}


function PComponent(props) {
  const styles = {
  "color": "V?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    V?`${AA.pointer} `:"  "
  );
}


function PComponent(props) {
  const styles = {
  "color": "V?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Create new agent"
  );
}


function PComponent(props) {
  const styles = {
  "color": "s"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    b?"":y?`${AA.pointer} `:"  "
  );
}


function PComponent(props) {
  const styles = {
  "color": "s"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    j.agentType
  );
}


function PComponent(props) {
  const styles = {
  "color": "y?warning:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",AA.warning," overridden by ",u
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(P,{bold:!0,color:"secondaryText"},j
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(v,{paddingLeft:2},oA.createElement(P,{bold:!0,color:"secondaryText"},j
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " (",c,"
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    G&&oA.createElement(v,{marginY:1},H(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "No agents found. Create specialized subagents that Claude can delegate to."
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Each subagent has its own context window, custom system prompt, and specific tools."
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Try creating: Code Reviewer, Code Simplifier, Security Reviewer, Tech Lead, or UX Reviewer."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(im,null
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(P,{dimColor:!0},F[F.length-1]
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G&&oA.createElement(v,{marginBottom:1},H(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(P,{color:"secondaryText"},oA.createElement(P,{bold:!0},"Built-in agents"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Built-in agents are provided by default and cannot be modified."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B.map((j
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oA.createElement(im,null
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rY.default.createElement(P,{color:F===0?"suggestion":void 0,bold:F===0},F===0?`${AA.pointer} `:"  ","[ Continue ]"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "─".repeat(40
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "─".repeat(40
  );
}


function PComponent(props) {
  const styles = {
  "color": "b?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    b?`${AA.pointer} `:"  ",y?`[ ${O.label} ]`:O.label
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rY.default.createElement(P,{dimColor:!0},V?"All tools selected":`${X.size} of ${A.length} tools selected`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    j3.createElement(P,{color:"secondaryText"},"Choose background color"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    z01.map((F,I
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    j3.createElement(P,{color:Y?"suggestion":void 0},Y?AA.pointer:" "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Automatic color"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    j3.createElement(P,{backgroundColor:F}," "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    F.charAt(0
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    j3.createElement(P,null,"Preview: "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",A," "
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "G"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",A," "
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},m0.createElement(vA,{key:"location-select",options:[{label:"Project (.claude/agents/
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},m0.createElement(vA,{key:"method-select",options:[{label:"Generate with Claude (recommended
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},G?m0.createElement(P,{dimColor:!0},B
  );
}


function T6Component(props) {
  const styles = {};
  
  return React.createElement(
    "t6component",
    { style: styles, ...props },
    focus:!0,showCursor:!0,columns:80,cursorOffset:D,onChangeCursorOffset:Z}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(xD,null
  );
}


function PComponent(props) {
  const styles = {
  "color": "suggestion"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Generating agent configuration…"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"error"},I
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1,flexDirection:"column"},m0.createElement(P,{bold:!0},"Description (when to use
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,A.whenToUse.length>80?A.whenToUse.slice(0,80
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{bold:!0},"System Prompt:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,A.systemPrompt.length>80?A.systemPrompt.slice(0,80
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{bold:!0},"Agent identifier:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(t6,{value:B,onChange:(Y
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"error"},F
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{dimColor:!0},"Claude suggested this identifier based on your description. You can edit it if needed."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,"Press ",m0.createElement(P,{bold:!0},"Enter"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Escape"
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},m0.createElement(t6,{value:Q,onChange:D,placeholder:Z,onSubmit:G,focus:!0,showCursor:!0,columns:80,cursorOffset:F,onChangeCursorOffset:I}
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"error"},Y
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},m0.createElement(t6,{value:Y,onChange:R,placeholder:"eg. use this agent after you're done writing code...",onSubmit:(c1
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(ab1,{agentName:A.agentType,currentColor:"automatic",onConfirm:(c1
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{marginTop:1},m0.createElement(t6,{value:J,onChange:j,placeholder:"Enter system prompt. Be comprehensive for best results",onSubmit:(
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    m0.createElement(v,{flexDirection:"column",marginTop:1},m0.createElement(P,null,m0.createElement(P,null,m0.createElement(P,{bold:!0},"Name"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Location"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Tools"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,m0.createElement(P,{bold:!0},"Description"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,A.agent.whenToUse.length>240?A.agent.whenToUse.slice(0,240
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,m0.createElement(P,{bold:!0},"System prompt"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,null,A.agent.systemPrompt.length>240?A.agent.systemPrompt.slice(0,240
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"warning"},"Warnings:"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","• ",c1
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"error"},"Errors:"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","• ",c1
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    m0.createElement(P,{color:"error"},V
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oY.createElement(P,{dimColor:!0},"Location: ",A.location
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    K.map((N,L
  );
}


function PComponent(props) {
  const styles = {
  "color": "L===F?suggestion:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    L===F?`${AA.pointer} `:"  ",N.label
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oY.createElement(P,{color:"error"},Y
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    WQ.createElement(P,{color:"secondaryText"},G
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    WQ.createElement(P,null,WQ.createElement(P,{bold:!0},"Description"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    WQ.createElement(P,null,A.whenToUse
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Tools"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Z.validTools.length>0&&WQ.createElement(P,null,Z.validTools.join(", "
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.warning," Unrecognized:"," ",Z.invalidTools.join(", "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Color"
  );
}


function PComponent(props) {
  const styles = {
  "backgroundColor": "F"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",A.agentType," "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "System prompt"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    WQ.createElement(P,null,DX(A.systemPrompt,D
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    tG1.createElement(P,{dimColor:!0},B.pending?`Press ${B.keyName} again to exit`:A
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    l2.createElement(v,{flexDirection:"column",marginTop:1},l2.createElement(vA,{options:j,onChange:b,onCancel:(
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l2.createElement(P,{dimColor:!0},J[J.length-1]
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    l2.createElement(xxB,{agent:R,tools:H,allAgents:Y,onBack:(
  );
}


function NFComponent(props) {
  const styles = {};
  
  return React.createElement(
    "nfcomponent",
    { style: styles, ...props },
    l2.createElement(P,null,"Are you sure you want to delete the agent"," ",l2.createElement(P,{bold:!0},Z.agent.agentType
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l2.createElement(P,{dimColor:!0},"Location: ",Z.agent.location
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    l2.createElement(vA,{options:T,onChange:(R
  );
}


function _xBComponent(props) {
  const styles = {};
  
  return React.createElement(
    "_xbcomponent",
    { style: styles, ...props },
    onBack:(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    MF.createElement(v,{flexDirection:"row"},MF.createElement(v,{minWidth:2},MF.createElement(P,{color:"text"},IM
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    MF.createElement(P,{bold:!0},"Compact summary",!Q&&MF.createElement(P,{dimColor:!0}," (ctrl+r to expand
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Large ",fB.createElement(P,{bold:!0},Q
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","• /memory to edit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "ULTRACLAUDE.md exceeds ",ce," chars (",B," chars
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ","• /memory to edit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Found invalid settings files. They will be ignored. Run /doctor for details."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Auth conflict: Using ",A.source," instead of Claude account subscription token. Either unset ",A.source,", or run `claude /logout`."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Auth conflict: Using ",A," instead of Anthropic Console key. Either unset ",A,", or run `claude /logout`."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(v,{flexDirection:"row"},fB.createElement(P,{color:"warning"},AA.warning
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Auth conflict: Both a token (",B.source,"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"warning"},"• Trying to use"," ",B.source==="claude.ai"?"claude.ai":B.source,"?"," ",A==="ANTHROPIC_API_KEY"?'Unset the ANTHROPIC_API_KEY environment variable, or claude /logout then say "No" to the API key approval before login.':A==="apiKeyHelper"?"Unset the apiKeyHelper setting.":"claude /logout"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• Trying to use ",A,"?"," ",B.source==="claude.ai"?"claude /logout to sign out of claude.ai.":`Unset the ${B.source} environment variable.`
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fB.createElement(P,{color:"secondaryText"},"What's new:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B.map((Q,D
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "• ",Q
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q.map((D
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "※ Tip: ",A.content
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    dz.createElement(P,{color:"suggestion"},"You can now use your Claude subscription with ",E2,dz.createElement(P,{color:"text",dimColor:!0}," ","• /login to activate"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    xL0.default.createElement(P,{dimColor:!0},D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    oB.createElement(P,{color:"warning"},AA.bullet
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " ",W1
  );
}


function VComponent(props) {
  const styles = {
  "width": "100%",
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y3.default.createElement(v,{borderStyle:"round",borderColor:"permission",flexDirection:"column",padding:1,width:"100%"},y3.default.createElement(v,null,y3.default.createElement(P,{color:"permission",bold:!0},"Export Conversation"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y3.default.createElement(P,{dimColor:!0},"Select export method:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y3.default.createElement(vA,{options:[{label:"Copy to clipboard",value:"clipboard",description:"Copy the conversation to your system clipboard"},{label:"Save to file",value:"file",description:"Save the conversation to a file in the current directory"}],onChange:(K
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y3.default.createElement(P,null,"Enter filename:"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    y3.default.createElement(P,null,">"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y?y3.default.createElement(P,{dimColor:!0},"Enter to save · Esc to go back"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press ",J.keyName," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Esc to cancel"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "height": "I?void 0:4+Math.min(Bf1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    !I&&w9.createElement(w9.Fragment,null,w9.createElement(v,{flexDirection:"column",minHeight:2,marginBottom:1},w9.createElement(P,{bold:!0},"Jump to a previous message"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "This will fork the conversation"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "height": "2"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w9.createElement(v,{width:7},R?w9.createElement(P,{color:"permission",bold:!0},AA.pointer," ",H+L+1," "
  );
}


function VComponent(props) {
  const styles = {
  "height": "1",
  "width": "100"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    O?w9.createElement(v,{width:"100%"},w9.createElement(P,{dimColor:!0,italic:!0},"(current
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "(empty message
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w9.createElement(P,{bold:!0},"What would you like to restore?"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    w9.createElement(P,{dimColor:!0},E.pending?w9.createElement(w9.Fragment,null,"Press ",E.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    IF1.createElement(P,{bold:!0,color:"permission"},A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    tY.createElement(v,{flexDirection:"column",padding:1},tY.createElement(P,{bold:!0,color:"permission"},"Opened changes in ",Z," ⧉"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Save file to continue…"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    tY.createElement(P,null,"Do you want to make this edit to"," ",tY.createElement(P,{bold:!0},OC8(D
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    vK.createElement(v,{borderColor:"secondaryBorder",borderStyle:D?"round":void 0,flexDirection:"column",paddingX:1},vK.createElement(v,{paddingBottom:1},vK.createElement(P,{bold:!0},Q?A:TC8(a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    N$.default.createElement(OI,{title:"Edit file"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    N$.default.createElement(P,null,"Do you want to make this edit to"," ",N$.default.createElement(P,{bold:!0},SC8(I
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Array.from(B.reasons.entries(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    g5.default.createElement(P,null,F," ",Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    A&&g5.default.createElement(P,null,A
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    g5.default.createElement(v,{flexDirection:"row"},g5.default.createElement(v,{justifyContent:"flex-end",minWidth:10},g5.default.createElement(P,{dimColor:!0},"Behavior "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    g5.default.createElement(v,{justifyContent:"flex-end",minWidth:10},g5.default.createElement(P,{dimColor:!0},"Message "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    g5.default.createElement(v,{justifyContent:"flex-end",minWidth:10},g5.default.createElement(P,{dimColor:!0},"Reason "
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    g5.default.createElement(v,{flexDirection:"column",alignItems:"flex-end",minWidth:10},g5.default.createElement(P,{dimColor:!0},"Suggested "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "rules "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.bullet," ",j5(Z
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bD.default.createElement(OI,{title:"Bash command"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bD.default.createElement(P,null,TQ.renderToolUseMessage({command:G,description:F},{theme:Z,verbose:!0}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B.description
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "flex-end"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bD.default.createElement(P,{dimColor:!0},"Ctrl-D to hide debug info"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bD.default.createElement(P,null,"Do you want to proceed?"
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "flex-end"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    bD.default.createElement(P,{dimColor:!0},"Ctrl-D to show debug info"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fK.default.createElement(OI,{title:"Tool use"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fK.default.createElement(P,null,I,"(",B.tool.renderToolUseMessage(B.input,{theme:G,verbose:Z}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " (MCP
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B.description
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    fK.default.createElement(P,null,"Do you want to proceed?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    hK.createElement(v,{paddingBottom:1},hK.createElement(P,{bold:!0},Q?A:uC8(a0(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rV.default.createElement(OI,{title:`${W?"Edit":"Create"} file`}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rV.default.createElement(dvB,{file_path:F,content:I,verbose:G}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    rV.default.createElement(P,null,"Do you want to ",W?"make this edit to":"create"," ",rV.default.createElement(P,{bold:!0},mC8(F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    lz.default.createElement(OI,{title:X}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    lz.default.createElement(P,null,Y,"(",A.tool.renderToolUseMessage(A.input,{theme:F,verbose:D}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    lz.default.createElement(P,null,"Do you want to proceed?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L$.default.createElement(OI,{title:"Fetch"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L$.default.createElement(P,null,ZJ.renderToolUseMessage(B.input,{theme:G,verbose:Z}
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    B.description
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L$.default.createElement(P,null,"Do you want to allow Claude to fetch this content?"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L7.createElement(v,{borderColor:"secondaryBorder",borderStyle:"round",flexDirection:"column",paddingX:1},L7.createElement(v,{paddingBottom:1,flexDirection:"column"},L7.createElement(P,{bold:!0},G?A:iC8(a0(
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    V," for cell ",B,D?` (${D}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L7.createElement(xV,{code:W,language:J}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    L7.createElement(xV,{code:Q,language:D==="markdown"?"markdown":J}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yM.default.createElement(OI,{title:`${I.edit_mode==="insert"?"Insert cell":I.edit_mode==="delete"?"Delete cell":"Edit cell"}`}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    yM.default.createElement(P,null,"Do you want to ",J," ",yM.default.createElement(P,{bold:!0},nC8(I.notebook_path
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    M$.default.createElement(OI,{title:"Edit file"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    M$.default.createElement(P,null,"Do you want to make this edit to"," ",M$.default.createElement(P,{bold:!0},sC8(I
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    kM.default.createElement(OI,{title:"Ready to code?"}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    kM.default.createElement(P,null,"Here is Claude's plan:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    kM.default.createElement(P,null,DX(A.input.plan,Z
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Would you like to proceed?"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    kM.default.createElement(vA,{options:[{label:"Yes, and auto-accept edits",value:"yes-accept-edits"},{label:"Yes, and manually approve edits",value:"yes-default"},{label:"No, keep planning",value:"no"}],onChange:(F
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Kf1.default.createElement(_P1,{inputState:Q,terminalFocus:!0,...A}
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Press ",A.key," again to exit"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Pasting text..."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.content.jsx
  );
}


function PComponent(props) {
  const styles = {
  "color": "D.content.color"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D.content.text
  );
}


function VComponent(props) {
  const styles = {
  "justifyContent": "flex-start"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Y?B4.createElement(P,{dimColor:!0,key:"vim-insert"},"-- INSERT --"
  );
}


function PComponent(props) {
  const styles = {
  "color": "remember"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "# to memorize"
  );
}


function PComponent(props) {
  const styles = {
  "color": "bashBorder"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "! for bash mode"
  );
}


function PComponent(props) {
  const styles = {
  "color": "planMode"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "⏸ plan mode on",B4.createElement(P,{color:"secondaryText",dimColor:!0}," ","(",bY.displayText," to cycle
  );
}


function PComponent(props) {
  const styles = {
  "color": "autoAccept"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "⏵⏵ auto-accept edits on",B4.createElement(P,{color:"secondaryText",dimColor:!0}," ","(",bY.displayText," to cycle
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    B4.createElement(P,{color:D?"text":"permission",inverse:D,bold:D},D?" ":"",G," ",G===1?"bash":"bashes"," ","running",D?" ":""
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "·"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D?"Enter to view shells":!F?"↓ to view":"? for shortcuts"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "? for shortcuts"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G&&Q8.createElement(P,{dimColor:!0},"globalVersion: ",F.global," · latestVersion:"," ",F.latest
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Auto-updating to v",F.latest,"…"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✓ Update installed · Restart to apply"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✗ Auto-update failed · Try ",Q8.createElement(P,{bold:!0},"claude doctor"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "npm i -g ",{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"1.0.61"}.PACKAGE_URL
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "cd ~/.claude/local && npm update ",{ISSUES_EXPLAINER:"report the issue at https://github.com/anthropics/claude-code/issues",PACKAGE_URL:"@anthropic-ai/claude-code",README_URL:"https://docs.anthropic.com/s/claude-code",VERSION:"1.0.61"}.PACKAGE_URL
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    G&&RF.createElement(P,{dimColor:!0},"current: ",F.current," · latest: ",F.latest
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Checking for updates"
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✓ Update installed · Restart to update"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "✗ Auto-update failed · Try ",RF.createElement(P,{bold:!0},"/status"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    tV.createElement(P,{color:E0(
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.circle," IDE disconnected"
  );
}


function PComponent(props) {
  const styles = {
  "color": "ide"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.circle,Z&&" IDE connected"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "IDE extension install failed (see /status for info
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "IDE plugin not connected (see /status for info
  );
}


function PComponent(props) {
  const styles = {
  "color": "ide"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "⧉ ",A.lineCount," ",A.lineCount===1?"line":"lines"," selected"
  );
}


function PComponent(props) {
  const styles = {
  "color": "ide"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "⧉ In ",mK8(A.filePath
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.circle," /ide for ",F3.createElement(P,{color:"ide"},F
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "IDE plugin not connected (see /status for info
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "alignItems": "flex-end"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    iQ.createElement(cbB,{ideSelection:W,mcpClients:X,ideInstallationStatus:J}
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Approaching Opus usage limit · /model to use best available model"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Approaching usage limit",O&&` · resets at ${O}`
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Invalid API key · Run /login"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Missing API key · Run /login"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Debug mode"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    C
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    G," tokens"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "Z?column:row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    zX.createElement(v,{width:Z?void 0:G},zX.createElement(P,{color:Q?"suggestion":void 0,dimColor:!Q},A.displayText
  );
}


function VComponent(props) {
  const styles = {
  "width": "D-(Z?4:G+4)"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    zX.createElement(P,{color:Q?"suggestion":void 0,dimColor:!Q,wrap:"wrap"},A.description
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    I.map((W
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V9.createElement(WM0,{suggestions:X,selectedSuggestion:V}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V9.createElement(v,{flexDirection:"column",width:22},V9.createElement(v,null,V9.createElement(P,{dimColor:!0},"! for bash mode"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "/ for commands"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "@ for file paths"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "# to memorize"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "35"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V9.createElement(v,null,V9.createElement(P,{dimColor:!0},"double tap esc to clear input"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    bY.displayText.replace("+"," + "
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "ctrl + r for verbose output"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    PbB(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V9.createElement(v,null,V9.createElement(P,{dimColor:!0},"ctrl + _ to undo"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "ctrl + z to suspend"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "row",
  "justifyContent": "space-between"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V9.createElement(jbB,{exitMessage:Q,vimMode:D,mode:Z,notification:C,toolPermissionContext:K,suppressHint:H,shellsSelected:$,isPasting:R}
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    $.length>0&&V5.createElement(v,{flexDirection:"column",marginTop:1},V5.createElement(v,{paddingLeft:2,flexDirection:"column",width:C5-4},V5.createElement(P,{color:"secondaryText",wrap:"wrap"},$.map((uA
  );
}


function VComponent(props) {
  const styles = {
  "alignItems": "flex-start",
  "justifyContent": "flex-start",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    V5.createElement(v,{alignItems:"flex-start",alignSelf:"flex-start",flexWrap:"nowrap",justifyContent:"flex-start",width:3},E==="bash"?V5.createElement(P,{color:"bashBorder",dimColor:F}," ! "
  );
}


function PComponent(props) {
  const styles = {
  "color": "remember"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " # "
  );
}


function PComponent(props) {
  const styles = {
  "color": "F?secondaryText:void 0"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " > "
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    ((
  );
}


function Bv1Component(props) {
  const styles = {};
  
  return React.createElement(
    "bv1component",
    { style: styles, ...props },
    onCancel:(
  );
}


function VComponent(props) {
  const styles = {
  "alignItems": "center",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    E4.createElement(P,{dimColor:!0},"Showing detailed transcript · Ctrl+R to toggle"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    E4.createElement(P,{dimColor:!0},O1
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "width": "100%"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    _9&&E4.createElement(Cm,{mode:C1,spinnerWords:nA,spinnerTips:PB,currentResponseLength:kB,overrideMessage:R7}
  );
}


function AfBComponent(props) {
  const styles = {};
  
  return React.createElement(
    "afbcomponent",
    { style: styles, ...props },
    onQuery:b0,verbose:$,messages:ZA,setToolJSX:X0,onAutoUpdaterResult:d1,autoUpdaterResult:v1,input:D2,onInputChange:N2,mode:_2,onModeChange:yB,queuedCommands:D4,setQueuedCommands:I2,submitCount:fA,onSubmitCountChange:(r1
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eY.default.createElement(P,{bold:!0,color:"success"},"Import MCP Servers from Claude Desktop"
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Note: Some servers already exist with the same name. If selected, they will be imported with a numbered suffix."
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eY.default.createElement(P,{dimColor:!0},I.pending?eY.default.createElement(eY.default.Fragment,null,"Press ",I.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TI.default.createElement(P,{bold:!0,color:"warning"},"Do you trust the files in this folder?"
  );
}


function PComponent(props) {
  const styles = {};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    x1(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TI.default.createElement(P,null,E2," may read files in this folder. Reading untrusted files may lead ",E2," to behave in unexpected ways."
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"Yes, proceed with MCP servers disabled",value:"yes_disable_mcp"},{label:"No, exit",value:"no"}]:[{label:"Yes, proceed",value:"yes_enable_mcp"},{label:"No, exit",value:"no"}],onChange:(G
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    TI.default.createElement(P,{dimColor:!0},Z.pending?TI.default.createElement(TI.default.Fragment,null,"Press ",Z.keyName," again to exit"
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    Nf1.default.createElement(O01,{initialPrompt:"",debug:Y,shouldShowPromptInput:!0,commands:B,initialTools:D,initialMessages:$.messages,initialTodos:N,mcpClients:Z,dynamicMcpConfig:G,strictMcpConfig:W,appendSystemPrompt:J}
  );
}


function DT1Component(props) {
  const styles = {};
  
  return React.createElement(
    "dt1component",
    { style: styles, ...props },
    "MCP documentation"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eV.default.createElement(P,{bold:!0,color:"warning"},A.length," new MCP servers found in .mcp.json"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    eV.default.createElement(P,{dimColor:!0},D.pending?eV.default.createElement(eV.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mK.default.createElement(P,{bold:!0,color:"warning"},"New MCP server found in .mcp.json: ",A
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"Use this MCP server",value:"yes"},{label:"Continue without using this MCP server",value:"no"}],onChange:(Z
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    mK.default.createElement(P,{dimColor:!0},D.pending?mK.default.createElement(mK.default.Fragment,null,"Press ",D.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column",
  "padding": "1"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    PI.default.createElement(P,{bold:!0,color:"error"},"WARNING: ",E2," running in Bypass Permissions mode"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    PI.default.createElement(P,null,"In Bypass Permissions mode, ",E2," will not ask for your approval before running potentially dangerous commands.",PI.default.createElement(H3,null
  );
}


function VAComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vacomponent",
    { style: styles, ...props },
    {label:"Yes, I accept",value:"accept"}],onChange:(D
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    PI.default.createElement(P,{dimColor:!0},Q.pending?PI.default.createElement(PI.default.Fragment,null,"Press ",Q.keyName," again to exit"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q4.default.createElement(v,null,Q4.default.createElement(P,{color:"warning"},AA.warning," Setup notes:"
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q4.default.createElement(P,{color:"secondaryText"},"• ",B
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.type==="checking"&&Q4.default.createElement(P,{color:"claude"},"Checking installation status..."
  );
}


function PComponent(props) {
  const styles = {
  "color": "warning"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Cleaning up old npm installations..."
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Installing Claude Code native build ",D.version,"..."
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Setting up launcher and shell integration..."
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.setupMessages&&Q4.default.createElement(WhB,{messages:D.setupMessages}
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    AA.tick," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "success"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Claude Code successfully installed!"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    D.version!=="current"&&Q4.default.createElement(v,null,Q4.default.createElement(P,{color:"secondaryText"},"Version: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D.version
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Location: "
  );
}


function PComponent(props) {
  const styles = {
  "color": "text"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    HH8(
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q4.default.createElement(v,{marginTop:1},Q4.default.createElement(P,{color:"secondaryText"},"Next: Run "
  );
}


function PComponent(props) {
  const styles = {
  "color": "claude"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "claude --help"
  );
}


function PComponent(props) {
  const styles = {
  "color": "secondaryText"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    " to get started"
  );
}


function VComponent(props) {
  const styles = {
  "flexDirection": "column"
};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q4.default.createElement(v,null,Q4.default.createElement(P,{color:"error"},AA.cross," "
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    "Installation failed"
  );
}


function PComponent(props) {
  const styles = {
  "color": "error"
};
  
  return React.createElement(
    "pcomponent",
    { style: styles, ...props },
    D.message
  );
}


function VComponent(props) {
  const styles = {};
  
  return React.createElement(
    "vcomponent",
    { style: styles, ...props },
    Q4.default.createElement(P,{color:"secondaryText"},"Try running with --force to override checks"
  );
}


function WH8Component(props) {
  const styles = {};
  
  return React.createElement(
    "wh8component",
    { style: styles, ...props },
    force:D,target:G}
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    M7.default.createElement(lMB,{onDone:async(
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    M7.default.createElement(Zv1,{customApiKeyTruncated:D,onDone:(
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    M7.default.createElement(O01,{debug:G,initialPrompt:x,shouldShowPromptInput:!0,commands:[...W1,...D0],initialTools:N1,initialMessages:C1.messages,initialTodos:n1,mcpClients:z1,dynamicMcpConfig:u,autoConnectIdeFlag:$,strictMcpConfig:T,appendSystemPrompt:Z.appendSystemPrompt}
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    M7.default.createElement(O01,{debug:G,initialPrompt:x,shouldShowPromptInput:!0,commands:[...W1,...D0],initialTools:N1,initialMessages:C1,mcpClients:z1,dynamicMcpConfig:u,autoConnectIdeFlag:$,strictMcpConfig:T,appendSystemPrompt:Z.appendSystemPrompt}
  );
}


function KDComponent(props) {
  const styles = {};
  
  return React.createElement(
    "kdcomponent",
    { style: styles, ...props },
    M7.default.createElement(O01,{debug:G,commands:[...W1,...D0],initialPrompt:x,shouldShowPromptInput:!0,initialTools:N1,initialTodos:C1,tipOfTheDay:c1,mcpClients:z1,dynamicMcpConfig:u,autoConnectIdeFlag:$,strictMcpConfig:T,appendSystemPrompt:Z.appendSystemPrompt}
  );
}


function MmComponent(props) {
  const styles = {};
  
  return React.createElement(
    "mmcomponent",
    { style: styles, ...props },
    mode:"setup-token",startingMessage:"This will guide you through long-lived (1-year
  );
}

export default {
  Component0: vComponent,
  Component1: vComponent,
  Component2: vComponent,
  Component3: vComponent,
  Component4: PComponent,
  Component5: vComponent,
  Component6: vComponent,
  Component7: PComponent,
  Component8: vComponent,
  Component9: PComponent,
  Component10: PComponent,
  Component11: Kp1Component,
  Component12: rV1Component,
  Component13: vComponent,
  Component14: vComponent,
  Component15: vComponent,
  Component16: PComponent,
  Component17: PComponent,
  Component18: vComponent,
  Component19: PComponent,
  Component20: vComponent,
  Component21: vComponent,
  Component22: vComponent,
  Component23: PComponent,
  Component24: PComponent,
  Component25: PComponent,
  Component26: PComponent,
  Component27: vComponent,
  Component28: PComponent,
  Component29: Yq2Component,
  Component30: PComponent,
  Component31: vComponent,
  Component32: vComponent,
  Component33: vComponent,
  Component34: vAComponent,
  Component35: PComponent,
  Component36: Tc4Component,
  Component37: dT1Component,
  Component38: PComponent,
  Component39: vComponent,
  Component40: vComponent,
  Component41: bAComponent,
  Component42: vComponent,
  Component43: PComponent,
  Component44: PComponent,
  Component45: bAComponent,
  Component46: vComponent,
  Component47: vComponent,
  Component48: PComponent,
  Component49: PComponent,
  Component50: PComponent,
  Component51: PComponent,
  Component52: PComponent,
  Component53: PComponent,
  Component54: vComponent,
  Component55: PComponent,
  Component56: PComponent,
  Component57: PComponent,
  Component58: vComponent,
  Component59: vComponent,
  Component60: PComponent,
  Component61: PComponent,
  Component62: vComponent,
  Component63: vComponent,
  Component64: PComponent,
  Component65: PComponent,
  Component66: PComponent,
  Component67: PComponent,
  Component68: PComponent,
  Component69: vComponent,
  Component70: PComponent,
  Component71: PComponent,
  Component72: vComponent,
  Component73: vComponent,
  Component74: vComponent,
  Component75: vComponent,
  Component76: PComponent,
  Component77: vComponent,
  Component78: PComponent,
  Component79: PComponent,
  Component80: bAComponent,
  Component81: bAComponent,
  Component82: PComponent,
  Component83: bAComponent,
  Component84: bAComponent,
  Component85: bAComponent,
  Component86: vComponent,
  Component87: PComponent,
  Component88: bAComponent,
  Component89: vComponent,
  Component90: vComponent,
  Component91: vComponent,
  Component92: PComponent,
  Component93: vComponent,
  Component94: vComponent,
  Component95: vComponent,
  Component96: PComponent,
  Component97: vComponent,
  Component98: vComponent,
  Component99: PComponent,
  Component100: PComponent,
  Component101: vComponent,
  Component102: vComponent,
  Component103: vComponent,
  Component104: vComponent,
  Component105: PComponent,
  Component106: PComponent,
  Component107: vComponent,
  Component108: PComponent,
  Component109: vComponent,
  Component110: vComponent,
  Component111: vComponent,
  Component112: vComponent,
  Component113: PComponent,
  Component114: PComponent,
  Component115: PComponent,
  Component116: PComponent,
  Component117: PComponent,
  Component118: PComponent,
  Component119: PComponent,
  Component120: PComponent,
  Component121: PComponent,
  Component122: PComponent,
  Component123: PComponent,
  Component124: PComponent,
  Component125: PComponent,
  Component126: PComponent,
  Component127: PComponent,
  Component128: vComponent,
  Component129: vComponent,
  Component130: PComponent,
  Component131: vAComponent,
  Component132: vComponent,
  Component133: vComponent,
  Component134: vComponent,
  Component135: vComponent,
  Component136: vComponent,
  Component137: vComponent,
  Component138: PComponent,
  Component139: vComponent,
  Component140: vComponent,
  Component141: vComponent,
  Component142: PComponent,
  Component143: vAComponent,
  Component144: vComponent,
  Component145: Ix1Component,
  Component146: vComponent,
  Component147: PComponent,
  Component148: vComponent,
  Component149: PComponent,
  Component150: PComponent,
  Component151: PComponent,
  Component152: PComponent,
  Component153: PComponent,
  Component154: PComponent,
  Component155: PComponent,
  Component156: PComponent,
  Component157: vComponent,
  Component158: PComponent,
  Component159: vComponent,
  Component160: vComponent,
  Component161: vComponent,
  Component162: vComponent,
  Component163: PComponent,
  Component164: PComponent,
  Component165: vComponent,
  Component166: PComponent,
  Component167: PComponent,
  Component168: PComponent,
  Component169: vComponent,
  Component170: PComponent,
  Component171: PComponent,
  Component172: PComponent,
  Component173: PComponent,
  Component174: PComponent,
  Component175: PComponent,
  Component176: vComponent,
  Component177: vComponent,
  Component178: vComponent,
  Component179: vComponent,
  Component180: PComponent,
  Component181: PComponent,
  Component182: PComponent,
  Component183: PComponent,
  Component184: vComponent,
  Component185: vComponent,
  Component186: vComponent,
  Component187: PComponent,
  Component188: PComponent,
  Component189: vComponent,
  Component190: PComponent,
  Component191: vComponent,
  Component192: PComponent,
  Component193: vComponent,
  Component194: PComponent,
  Component195: PComponent,
  Component196: vComponent,
  Component197: vComponent,
  Component198: vComponent,
  Component199: vComponent,
  Component200: vComponent,
  Component201: vComponent,
  Component202: vComponent,
  Component203: vComponent,
  Component204: vComponent,
  Component205: vComponent,
  Component206: vComponent,
  Component207: vComponent,
  Component208: vComponent,
  Component209: PComponent,
  Component210: PComponent,
  Component211: PComponent,
  Component212: vComponent,
  Component213: vComponent,
  Component214: PComponent,
  Component215: PComponent,
  Component216: PComponent,
  Component217: vComponent,
  Component218: vComponent,
  Component219: PComponent,
  Component220: PComponent,
  Component221: vComponent,
  Component222: vComponent,
  Component223: vComponent,
  Component224: PComponent,
  Component225: PComponent,
  Component226: vComponent,
  Component227: vComponent,
  Component228: PComponent,
  Component229: PComponent,
  Component230: PComponent,
  Component231: PComponent,
  Component232: PComponent,
  Component233: vComponent,
  Component234: vComponent,
  Component235: vComponent,
  Component236: vComponent,
  Component237: vComponent,
  Component238: vComponent,
  Component239: vComponent,
  Component240: vComponent,
  Component241: vComponent,
  Component242: vComponent,
  Component243: PComponent,
  Component244: vComponent,
  Component245: vComponent,
  Component246: vComponent,
  Component247: vComponent,
  Component248: vComponent,
  Component249: vComponent,
  Component250: vComponent,
  Component251: vComponent,
  Component252: vComponent,
  Component253: vComponent,
  Component254: vComponent,
  Component255: vComponent,
  Component256: PComponent,
  Component257: vAComponent,
  Component258: vComponent,
  Component259: vComponent,
  Component260: vComponent,
  Component261: PComponent,
  Component262: vComponent,
  Component263: PComponent,
  Component264: vComponent,
  Component265: vComponent,
  Component266: PComponent,
  Component267: PComponent,
  Component268: vComponent,
  Component269: vComponent,
  Component270: vAComponent,
  Component271: PComponent,
  Component272: vComponent,
  Component273: vComponent,
  Component274: PComponent,
  Component275: PComponent,
  Component276: vComponent,
  Component277: vComponent,
  Component278: vComponent,
  Component279: vComponent,
  Component280: vComponent,
  Component281: vComponent,
  Component282: vComponent,
  Component283: vComponent,
  Component284: PComponent,
  Component285: PComponent,
  Component286: vComponent,
  Component287: vComponent,
  Component288: vComponent,
  Component289: vComponent,
  Component290: vComponent,
  Component291: vComponent,
  Component292: vComponent,
  Component293: vComponent,
  Component294: vComponent,
  Component295: PComponent,
  Component296: PComponent,
  Component297: vComponent,
  Component298: vComponent,
  Component299: PComponent,
  Component300: vComponent,
  Component301: PComponent,
  Component302: vComponent,
  Component303: PComponent,
  Component304: PComponent,
  Component305: PComponent,
  Component306: PComponent,
  Component307: PComponent,
  Component308: vComponent,
  Component309: vComponent,
  Component310: vComponent,
  Component311: PComponent,
  Component312: vComponent,
  Component313: vComponent,
  Component314: vComponent,
  Component315: vComponent,
  Component316: vComponent,
  Component317: vComponent,
  Component318: vComponent,
  Component319: vComponent,
  Component320: vComponent,
  Component321: vComponent,
  Component322: PComponent,
  Component323: vComponent,
  Component324: vComponent,
  Component325: PComponent,
  Component326: vComponent,
  Component327: vComponent,
  Component328: vComponent,
  Component329: vComponent,
  Component330: vComponent,
  Component331: vComponent,
  Component332: vComponent,
  Component333: PComponent,
  Component334: vComponent,
  Component335: vComponent,
  Component336: vComponent,
  Component337: vComponent,
  Component338: vComponent,
  Component339: PComponent,
  Component340: vComponent,
  Component341: vComponent,
  Component342: PComponent,
  Component343: PComponent,
  Component344: vComponent,
  Component345: vComponent,
  Component346: vComponent,
  Component347: vComponent,
  Component348: vComponent,
  Component349: PComponent,
  Component350: PComponent,
  Component351: vComponent,
  Component352: vComponent,
  Component353: vComponent,
  Component354: vComponent,
  Component355: vComponent,
  Component356: vComponent,
  Component357: PComponent,
  Component358: vComponent,
  Component359: PComponent,
  Component360: vComponent,
  Component361: vComponent,
  Component362: vComponent,
  Component363: PComponent,
  Component364: vComponent,
  Component365: vComponent,
  Component366: PComponent,
  Component367: vComponent,
  Component368: vComponent,
  Component369: vComponent,
  Component370: PComponent,
  Component371: vComponent,
  Component372: vComponent,
  Component373: vComponent,
  Component374: PComponent,
  Component375: vComponent,
  Component376: vComponent,
  Component377: vComponent,
  Component378: PComponent,
  Component379: vComponent,
  Component380: vComponent,
  Component381: vComponent,
  Component382: vComponent,
  Component383: PComponent,
  Component384: vComponent,
  Component385: PComponent,
  Component386: PComponent,
  Component387: vComponent,
  Component388: vComponent,
  Component389: PComponent,
  Component390: vComponent,
  Component391: PComponent,
  Component392: PComponent,
  Component393: vComponent,
  Component394: vComponent,
  Component395: PComponent,
  Component396: vComponent,
  Component397: vComponent,
  Component398: vComponent,
  Component399: vComponent,
  Component400: vComponent,
  Component401: vComponent,
  Component402: vComponent,
  Component403: vComponent,
  Component404: vComponent,
  Component405: vComponent,
  Component406: vComponent,
  Component407: vComponent,
  Component408: vComponent,
  Component409: PComponent,
  Component410: vComponent,
  Component411: vComponent,
  Component412: vComponent,
  Component413: vComponent,
  Component414: vComponent,
  Component415: vComponent,
  Component416: vComponent,
  Component417: vComponent,
  Component418: vAComponent,
  Component419: vComponent,
  Component420: vComponent,
  Component421: vComponent,
  Component422: vComponent,
  Component423: vComponent,
  Component424: vComponent,
  Component425: PComponent,
  Component426: PComponent,
  Component427: vComponent,
  Component428: vComponent,
  Component429: PComponent,
  Component430: PComponent,
  Component431: PComponent,
  Component432: PComponent,
  Component433: PComponent,
  Component434: PComponent,
  Component435: PComponent,
  Component436: PComponent,
  Component437: vComponent,
  Component438: vComponent,
  Component439: vComponent,
  Component440: vComponent,
  Component441: PComponent,
  Component442: vComponent,
  Component443: vComponent,
  Component444: PComponent,
  Component445: PComponent,
  Component446: PComponent,
  Component447: PComponent,
  Component448: PComponent,
  Component449: PComponent,
  Component450: vComponent,
  Component451: vComponent,
  Component452: vComponent,
  Component453: vComponent,
  Component454: PComponent,
  Component455: PComponent,
  Component456: vAComponent,
  Component457: vComponent,
  Component458: vComponent,
  Component459: PComponent,
  Component460: PComponent,
  Component461: PComponent,
  Component462: vComponent,
  Component463: PComponent,
  Component464: PComponent,
  Component465: PComponent,
  Component466: vComponent,
  Component467: PComponent,
  Component468: vComponent,
  Component469: vComponent,
  Component470: PComponent,
  Component471: PComponent,
  Component472: PComponent,
  Component473: vComponent,
  Component474: vComponent,
  Component475: PComponent,
  Component476: PComponent,
  Component477: PComponent,
  Component478: PComponent,
  Component479: vComponent,
  Component480: bAComponent,
  Component481: vComponent,
  Component482: vComponent,
  Component483: vComponent,
  Component484: vComponent,
  Component485: bAComponent,
  Component486: vComponent,
  Component487: PComponent,
  Component488: vComponent,
  Component489: bAComponent,
  Component490: bAComponent,
  Component491: PComponent,
  Component492: PComponent,
  Component493: PComponent,
  Component494: vComponent,
  Component495: PComponent,
  Component496: PComponent,
  Component497: vComponent,
  Component498: PComponent,
  Component499: vComponent,
  Component500: vComponent,
  Component501: vComponent,
  Component502: PComponent,
  Component503: PComponent,
  Component504: PComponent,
  Component505: PComponent,
  Component506: PComponent,
  Component507: PComponent,
  Component508: vComponent,
  Component509: vComponent,
  Component510: PComponent,
  Component511: PComponent,
  Component512: PComponent,
  Component513: vComponent,
  Component514: vComponent,
  Component515: PComponent,
  Component516: PComponent,
  Component517: PComponent,
  Component518: PComponent,
  Component519: vComponent,
  Component520: vComponent,
  Component521: PComponent,
  Component522: vComponent,
  Component523: vComponent,
  Component524: PComponent,
  Component525: PComponent,
  Component526: PComponent,
  Component527: PComponent,
  Component528: PComponent,
  Component529: vComponent,
  Component530: vComponent,
  Component531: lv1Component,
  Component532: vComponent,
  Component533: vComponent,
  Component534: bAComponent,
  Component535: vComponent,
  Component536: vComponent,
  Component537: bAComponent,
  Component538: vComponent,
  Component539: vComponent,
  Component540: PComponent,
  Component541: PComponent,
  Component542: PComponent,
  Component543: vComponent,
  Component544: vComponent,
  Component545: PComponent,
  Component546: vComponent,
  Component547: PComponent,
  Component548: vComponent,
  Component549: PComponent,
  Component550: vComponent,
  Component551: vComponent,
  Component552: vComponent,
  Component553: vComponent,
  Component554: PComponent,
  Component555: vComponent,
  Component556: PComponent,
  Component557: vComponent,
  Component558: vComponent,
  Component559: vComponent,
  Component560: vComponent,
  Component561: PComponent,
  Component562: PComponent,
  Component563: vComponent,
  Component564: vComponent,
  Component565: PComponent,
  Component566: vComponent,
  Component567: PComponent,
  Component568: vComponent,
  Component569: vAComponent,
  Component570: vComponent,
  Component571: vComponent,
  Component572: vComponent,
  Component573: PComponent,
  Component574: vComponent,
  Component575: vComponent,
  Component576: vComponent,
  Component577: vComponent,
  Component578: vComponent,
  Component579: vComponent,
  Component580: vComponent,
  Component581: vComponent,
  Component582: vComponent,
  Component583: PComponent,
  Component584: vComponent,
  Component585: vAComponent,
  Component586: vComponent,
  Component587: PComponent,
  Component588: vComponent,
  Component589: vComponent,
  Component590: vComponent,
  Component591: vComponent,
  Component592: vComponent,
  Component593: vComponent,
  Component594: vComponent,
  Component595: vComponent,
  Component596: PComponent,
  Component597: vComponent,
  Component598: vComponent,
  Component599: vComponent,
  Component600: vComponent,
  Component601: vComponent,
  Component602: vComponent,
  Component603: PComponent,
  Component604: PComponent,
  Component605: PComponent,
  Component606: vComponent,
  Component607: vComponent,
  Component608: PComponent,
  Component609: PComponent,
  Component610: vComponent,
  Component611: PComponent,
  Component612: PComponent,
  Component613: vComponent,
  Component614: vComponent,
  Component615: vComponent,
  Component616: PComponent,
  Component617: vAComponent,
  Component618: vComponent,
  Component619: vComponent,
  Component620: vComponent,
  Component621: vComponent,
  Component622: vComponent,
  Component623: vComponent,
  Component624: vComponent,
  Component625: vComponent,
  Component626: PComponent,
  Component627: PComponent,
  Component628: PComponent,
  Component629: vAComponent,
  Component630: vComponent,
  Component631: PComponent,
  Component632: PComponent,
  Component633: PComponent,
  Component634: vComponent,
  Component635: vComponent,
  Component636: PComponent,
  Component637: vComponent,
  Component638: PComponent,
  Component639: vComponent,
  Component640: PComponent,
  Component641: vComponent,
  Component642: vComponent,
  Component643: PComponent,
  Component644: vComponent,
  Component645: PComponent,
  Component646: PComponent,
  Component647: vComponent,
  Component648: PComponent,
  Component649: vComponent,
  Component650: PComponent,
  Component651: vComponent,
  Component652: vComponent,
  Component653: PComponent,
  Component654: bAComponent,
  Component655: vComponent,
  Component656: PComponent,
  Component657: PComponent,
  Component658: vComponent,
  Component659: PComponent,
  Component660: vComponent,
  Component661: vComponent,
  Component662: bAComponent,
  Component663: PComponent,
  Component664: PComponent,
  Component665: vComponent,
  Component666: vComponent,
  Component667: bAComponent,
  Component668: bAComponent,
  Component669: PComponent,
  Component670: bAComponent,
  Component671: vComponent,
  Component672: vComponent,
  Component673: vComponent,
  Component674: vComponent,
  Component675: vComponent,
  Component676: vComponent,
  Component677: PComponent,
  Component678: PComponent,
  Component679: vComponent,
  Component680: bAComponent,
  Component681: bAComponent,
  Component682: bAComponent,
  Component683: bAComponent,
  Component684: bAComponent,
  Component685: vComponent,
  Component686: bAComponent,
  Component687: PComponent,
  Component688: vComponent,
  Component689: vComponent,
  Component690: vComponent,
  Component691: PComponent,
  Component692: vComponent,
  Component693: vComponent,
  Component694: vComponent,
  Component695: vComponent,
  Component696: PComponent,
  Component697: bAComponent,
  Component698: SmComponent,
  Component699: PComponent,
  Component700: bAComponent,
  Component701: bAComponent,
  Component702: bAComponent,
  Component703: vComponent,
  Component704: vComponent,
  Component705: vComponent,
  Component706: vComponent,
  Component707: PComponent,
  Component708: bAComponent,
  Component709: PComponent,
  Component710: lb1Component,
  Component711: PComponent,
  Component712: vComponent,
  Component713: PComponent,
  Component714: vComponent,
  Component715: vComponent,
  Component716: vComponent,
  Component717: vComponent,
  Component718: bAComponent,
  Component719: vComponent,
  Component720: vComponent,
  Component721: bAComponent,
  Component722: bAComponent,
  Component723: vComponent,
  Component724: vComponent,
  Component725: vComponent,
  Component726: PComponent,
  Component727: PComponent,
  Component728: PComponent,
  Component729: vComponent,
  Component730: vComponent,
  Component731: vComponent,
  Component732: vComponent,
  Component733: vComponent,
  Component734: PComponent,
  Component735: vComponent,
  Component736: PComponent,
  Component737: PComponent,
  Component738: PComponent,
  Component739: PComponent,
  Component740: PComponent,
  Component741: vComponent,
  Component742: vComponent,
  Component743: PComponent,
  Component744: NFComponent,
  Component745: PComponent,
  Component746: PComponent,
  Component747: PComponent,
  Component748: vComponent,
  Component749: vComponent,
  Component750: vComponent,
  Component751: vComponent,
  Component752: PComponent,
  Component753: vComponent,
  Component754: vComponent,
  Component755: vComponent,
  Component756: PComponent,
  Component757: PComponent,
  Component758: PComponent,
  Component759: vComponent,
  Component760: vComponent,
  Component761: vComponent,
  Component762: vComponent,
  Component763: PComponent,
  Component764: vComponent,
  Component765: PComponent,
  Component766: vComponent,
  Component767: PComponent,
  Component768: PComponent,
  Component769: NFComponent,
  Component770: NFComponent,
  Component771: NFComponent,
  Component772: t6Component,
  Component773: vComponent,
  Component774: PComponent,
  Component775: vComponent,
  Component776: NFComponent,
  Component777: vComponent,
  Component778: vComponent,
  Component779: vComponent,
  Component780: vComponent,
  Component781: vComponent,
  Component782: vComponent,
  Component783: vComponent,
  Component784: vComponent,
  Component785: PComponent,
  Component786: NFComponent,
  Component787: vComponent,
  Component788: NFComponent,
  Component789: vComponent,
  Component790: NFComponent,
  Component791: NFComponent,
  Component792: PComponent,
  Component793: PComponent,
  Component794: vComponent,
  Component795: vComponent,
  Component796: vComponent,
  Component797: vComponent,
  Component798: vComponent,
  Component799: PComponent,
  Component800: vComponent,
  Component801: PComponent,
  Component802: vComponent,
  Component803: vComponent,
  Component804: vComponent,
  Component805: PComponent,
  Component806: vComponent,
  Component807: vComponent,
  Component808: vComponent,
  Component809: vComponent,
  Component810: PComponent,
  Component811: vComponent,
  Component812: PComponent,
  Component813: PComponent,
  Component814: PComponent,
  Component815: PComponent,
  Component816: vComponent,
  Component817: vComponent,
  Component818: NFComponent,
  Component819: vComponent,
  Component820: NFComponent,
  Component821: NFComponent,
  Component822: vComponent,
  Component823: vComponent,
  Component824: _xBComponent,
  Component825: vComponent,
  Component826: vComponent,
  Component827: vComponent,
  Component828: PComponent,
  Component829: PComponent,
  Component830: vComponent,
  Component831: PComponent,
  Component832: PComponent,
  Component833: vComponent,
  Component834: PComponent,
  Component835: vComponent,
  Component836: PComponent,
  Component837: vComponent,
  Component838: PComponent,
  Component839: vComponent,
  Component840: PComponent,
  Component841: vComponent,
  Component842: PComponent,
  Component843: vComponent,
  Component844: vComponent,
  Component845: PComponent,
  Component846: vComponent,
  Component847: PComponent,
  Component848: vComponent,
  Component849: vComponent,
  Component850: vComponent,
  Component851: PComponent,
  Component852: vComponent,
  Component853: vComponent,
  Component854: vComponent,
  Component855: vComponent,
  Component856: vComponent,
  Component857: vComponent,
  Component858: PComponent,
  Component859: PComponent,
  Component860: vComponent,
  Component861: PComponent,
  Component862: vComponent,
  Component863: vComponent,
  Component864: PComponent,
  Component865: vComponent,
  Component866: vComponent,
  Component867: vComponent,
  Component868: vComponent,
  Component869: PComponent,
  Component870: vComponent,
  Component871: vComponent,
  Component872: vComponent,
  Component873: vComponent,
  Component874: vComponent,
  Component875: vComponent,
  Component876: vComponent,
  Component877: vComponent,
  Component878: vComponent,
  Component879: vComponent,
  Component880: vComponent,
  Component881: PComponent,
  Component882: PComponent,
  Component883: vComponent,
  Component884: vComponent,
  Component885: PComponent,
  Component886: vComponent,
  Component887: vComponent,
  Component888: vComponent,
  Component889: vComponent,
  Component890: vComponent,
  Component891: PComponent,
  Component892: PComponent,
  Component893: vComponent,
  Component894: vComponent,
  Component895: vComponent,
  Component896: vComponent,
  Component897: vComponent,
  Component898: vComponent,
  Component899: vComponent,
  Component900: vComponent,
  Component901: vComponent,
  Component902: vComponent,
  Component903: PComponent,
  Component904: vComponent,
  Component905: vComponent,
  Component906: PComponent,
  Component907: vComponent,
  Component908: vComponent,
  Component909: vComponent,
  Component910: vComponent,
  Component911: vComponent,
  Component912: vComponent,
  Component913: vComponent,
  Component914: vComponent,
  Component915: vComponent,
  Component916: PComponent,
  Component917: vComponent,
  Component918: vComponent,
  Component919: PComponent,
  Component920: PComponent,
  Component921: vComponent,
  Component922: PComponent,
  Component923: vComponent,
  Component924: PComponent,
  Component925: PComponent,
  Component926: PComponent,
  Component927: PComponent,
  Component928: vComponent,
  Component929: PComponent,
  Component930: PComponent,
  Component931: PComponent,
  Component932: vComponent,
  Component933: PComponent,
  Component934: PComponent,
  Component935: PComponent,
  Component936: PComponent,
  Component937: PComponent,
  Component938: vComponent,
  Component939: PComponent,
  Component940: PComponent,
  Component941: PComponent,
  Component942: vComponent,
  Component943: PComponent,
  Component944: PComponent,
  Component945: PComponent,
  Component946: PComponent,
  Component947: PComponent,
  Component948: PComponent,
  Component949: PComponent,
  Component950: PComponent,
  Component951: PComponent,
  Component952: vComponent,
  Component953: PComponent,
  Component954: PComponent,
  Component955: PComponent,
  Component956: PComponent,
  Component957: PComponent,
  Component958: PComponent,
  Component959: PComponent,
  Component960: vComponent,
  Component961: vComponent,
  Component962: vComponent,
  Component963: vComponent,
  Component964: vComponent,
  Component965: PComponent,
  Component966: PComponent,
  Component967: PComponent,
  Component968: vComponent,
  Component969: PComponent,
  Component970: PComponent,
  Component971: PComponent,
  Component972: vComponent,
  Component973: PComponent,
  Component974: vComponent,
  Component975: vComponent,
  Component976: vComponent,
  Component977: PComponent,
  Component978: PComponent,
  Component979: vComponent,
  Component980: Bv1Component,
  Component981: vComponent,
  Component982: vComponent,
  Component983: vComponent,
  Component984: AfBComponent,
  Component985: vComponent,
  Component986: PComponent,
  Component987: vComponent,
  Component988: vComponent,
  Component989: PComponent,
  Component990: vComponent,
  Component991: vAComponent,
  Component992: vComponent,
  Component993: kDComponent,
  Component994: dT1Component,
  Component995: vComponent,
  Component996: vComponent,
  Component997: vComponent,
  Component998: vAComponent,
  Component999: vComponent,
  Component1000: vComponent,
  Component1001: vComponent,
  Component1002: vAComponent,
  Component1003: vComponent,
  Component1004: vComponent,
  Component1005: vComponent,
  Component1006: vComponent,
  Component1007: PComponent,
  Component1008: PComponent,
  Component1009: PComponent,
  Component1010: vComponent,
  Component1011: PComponent,
  Component1012: PComponent,
  Component1013: vComponent,
  Component1014: PComponent,
  Component1015: PComponent,
  Component1016: PComponent,
  Component1017: vComponent,
  Component1018: PComponent,
  Component1019: PComponent,
  Component1020: vComponent,
  Component1021: PComponent,
  Component1022: PComponent,
  Component1023: vComponent,
  Component1024: wH8Component,
  Component1025: kDComponent,
  Component1026: kDComponent,
  Component1027: kDComponent,
  Component1028: kDComponent,
  Component1029: kDComponent,
  Component1030: MmComponent
};