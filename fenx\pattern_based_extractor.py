#!/usr/bin/env python3
"""
基于已知模式的Claude Code UI提取器
使用分析项目中的知识来定位和提取UI组件
"""

import re
import json
import os

class PatternBasedExtractor:
    def __init__(self, file_path):
        self.file_path = file_path
        self.content = ""
        # 基于分析项目的已知函数映射
        self.known_functions = {
            'y2A': {'desc': '欢迎界面渲染器', 'type': 'component'},
            'k2A': {'desc': 'GA统计展示', 'type': 'component'},
            'j2A': {'desc': '工具使用统计组件', 'type': 'component'},
            'Wy2': {'desc': '选择变化监听器', 'type': 'listener'},
            'xy2': {'desc': '信任确认对话框', 'type': 'dialog'},
            'c9': {'desc': '终端尺寸管理器', 'type': 'utility'},
            'Je0': {'desc': 'IDE安装成功显示', 'type': 'component'},
            'Z0': {'desc': '全局键盘监听器', 'type': 'listener'},
            'Fq5': {'desc': '进度条组件', 'type': 'component'},
            'BE': {'desc': '通用内容显示组件', 'type': 'component'},
            'X45': {'desc': '内容智能折叠', 'type': 'utility'},
            'LV2': {'desc': '多行内容格式化', 'type': 'utility'},
            'W45': {'desc': 'JSON格式化器', 'type': 'utility'},
            'xN5': {'desc': '模型选择菜单', 'type': 'dialog'},
            'DE1': {'desc': 'MCP安全提示', 'type': 'component'}
        }
        
        self.extracted_components = []
        self.ui_patterns = []
        self.color_definitions = {}
        
    def load_file(self):
        """加载文件"""
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
            self.content = f.read()
        print(f"文件已加载: {len(self.content):,} 字符")
        
    def extract_known_functions(self):
        """提取已知的UI函数"""
        print("\n查找已知UI函数...")
        found = []
        
        for func_name, info in self.known_functions.items():
            # 查找函数定义
            patterns = [
                f'function\\s+{func_name}\\s*\\([^{{]*\\)\\s*{{',
                f'{func_name}\\s*=\\s*function\\s*\\([^{{]*\\)\\s*{{',
                f'{func_name}\\s*=\\s*\\([^{{]*\\)\\s*=>\\s*{{',
                f'const\\s+{func_name}\\s*=',
                f'let\\s+{func_name}\\s*=',
                f'var\\s+{func_name}\\s*='
            ]
            
            for pattern in patterns:
                match = re.search(pattern, self.content)
                if match:
                    # 提取函数体的一部分
                    start = match.start()
                    snippet = self.content[start:start+200].replace('\n', ' ')
                    found.append({
                        'name': func_name,
                        'type': info['type'],
                        'description': info['desc'],
                        'snippet': snippet[:150] + '...'
                    })
                    print(f"[Found] {func_name}: {info['desc']}")
                    break
                    
        self.extracted_components = found
        
    def extract_ui_patterns(self):
        """提取UI模式特征"""
        print("\n提取UI模式...")
        
        # 查找createElement调用
        create_patterns = [
            r'([a-zA-Z_$][\w$]*(?:\.default)?\.createElement)\s*\(',
            r'(createElement)\s*\('
        ]
        
        for pattern in create_patterns:
            matches = re.findall(pattern, self.content[:500000])
            if matches:
                unique = list(set(matches))[:10]
                print(f"找到createElement变体: {unique}")
                self.ui_patterns.append({
                    'type': 'createElement',
                    'variants': unique,
                    'count': len(matches)
                })
                
        # 查找组件名称模式（通常在createElement的第一个参数）
        comp_pattern = r'createElement\s*\(\s*([A-Z][\w$]*|[a-zA-Z_$][\w$]*|"[^"]+"|\'[^\']+\')'
        comp_matches = re.findall(comp_pattern, self.content[:200000])
        if comp_matches:
            components = []
            for comp in comp_matches[:50]:
                if not comp.startswith('"') and not comp.startswith("'"):
                    components.append(comp)
            
            unique_comps = list(set(components))[:20]
            if unique_comps:
                print(f"找到组件名称: {unique_comps[:10]}")
                self.ui_patterns.append({
                    'type': 'components',
                    'names': unique_comps
                })
                
    def extract_color_theme(self):
        """提取颜色主题"""
        print("\n提取颜色主题...")
        
        # 已知的颜色名称
        known_colors = {
            'claude': '#FF6B35',
            'success': '#00C851',
            'error': '#FF4444',
            'warning': '#FFBB33',
            'remember': '#33B5E5',
            'bashBorder': '#AA7942',
            'secondaryBorder': '#666666',
            'text': '#FFFFFF',
            'permission': '#9C27B0'
        }
        
        # 查找颜色定义
        for color_name in known_colors:
            # 多种可能的定义模式
            patterns = [
                f'{color_name}\\s*[:=]\\s*["\']([^"\']+)["\']',
                f'["\']color["\']\\s*[:=]\\s*["\']{color_name}["\']',
                f'color\\s*[:=]\\s*["\']{color_name}["\']'
            ]
            
            for pattern in patterns:
                match = re.search(pattern, self.content, re.IGNORECASE)
                if match:
                    print(f"[Found] Color: {color_name}")
                    self.color_definitions[color_name] = True
                    break
                    
    def extract_layout_info(self):
        """提取布局信息"""
        print("\n提取布局属性...")
        
        layout_props = {
            'flexDirection': ['column', 'row', 'row-reverse', 'column-reverse'],
            'gap': [],
            'padding': [],
            'margin': [],
            'borderStyle': ['round', 'single', 'double', 'bold'],
            'alignItems': ['center', 'flex-start', 'flex-end', 'stretch'],
            'justifyContent': ['center', 'space-between', 'flex-start', 'flex-end']
        }
        
        found_layouts = {}
        
        for prop, expected_values in layout_props.items():
            pattern = f'{prop}\\s*[:=]\\s*["\']?([^,;\\s\\}}"\'\\)]+)'
            matches = re.findall(pattern, self.content[:300000], re.IGNORECASE)
            
            if matches:
                values = list(set(matches))[:10]
                # 过滤出有效值
                if expected_values:
                    values = [v for v in values if v in expected_values] + [v for v in values if v not in expected_values][:3]
                    
                if values:
                    found_layouts[prop] = values
                    print(f"[Found] {prop}: {values[:5]}")
                    
        return found_layouts
        
    def generate_extraction_report(self):
        """生成提取报告"""
        output_dir = os.path.dirname(self.file_path) + '/fenx'
        
        # 生成Markdown报告
        md_path = os.path.join(output_dir, 'ui_extraction_report.md')
        with open(md_path, 'w', encoding='utf-8') as f:
            f.write("# Claude Code UI组件提取报告\n\n")
            f.write("基于逆向分析项目的知识进行精确提取\n\n")
            
            f.write("## 已找到的UI函数\n\n")
            if self.extracted_components:
                f.write("| 函数名 | 类型 | 描述 | 代码片段 |\n")
                f.write("|--------|------|------|----------|\n")
                for comp in self.extracted_components:
                    f.write(f"| {comp['name']} | {comp['type']} | {comp['description']} | `{comp['snippet'][:50]}...` |\n")
            else:
                f.write("未找到已知的UI函数\n")
                
            f.write("\n## UI模式\n\n")
            for pattern in self.ui_patterns:
                if pattern['type'] == 'createElement':
                    f.write(f"### createElement变体\n")
                    f.write(f"- 总调用次数: {pattern['count']}\n")
                    f.write(f"- 变体: {', '.join(pattern['variants'][:5])}\n\n")
                elif pattern['type'] == 'components':
                    f.write(f"### 发现的组件\n")
                    for comp in pattern['names'][:15]:
                        f.write(f"- {comp}\n")
                        
            f.write("\n## 颜色主题\n\n")
            if self.color_definitions:
                f.write("已确认的主题颜色:\n\n")
                for color in self.color_definitions:
                    f.write(f"- [v] {color}\n")
                    
            f.write("\n## 布局属性\n\n")
            layouts = self.extract_layout_info()
            for prop, values in layouts.items():
                f.write(f"### {prop}\n")
                f.write(f"值: {', '.join(str(v) for v in values)}\n\n")
                
        print(f"\n报告已生成: {md_path}")
        
        # 生成JSON数据
        json_path = os.path.join(output_dir, 'ui_extraction_data.json')
        data = {
            'extracted_functions': self.extracted_components,
            'ui_patterns': self.ui_patterns,
            'color_theme': self.color_definitions,
            'layout_properties': layouts
        }
        
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(data, f, indent=2, ensure_ascii=False)
            
        print(f"数据已保存: {json_path}")

def main():
    extractor = PatternBasedExtractor(r"E:\claude\yuan\package\cli.js")
    
    extractor.load_file()
    extractor.extract_known_functions()
    extractor.extract_ui_patterns()
    extractor.extract_color_theme()
    extractor.generate_extraction_report()
    
    print("\n提取完成！")

if __name__ == "__main__":
    main()