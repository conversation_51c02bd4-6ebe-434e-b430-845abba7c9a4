#!/usr/bin/env python3
"""
深度分析Claude Code的混淆代码
专注于提取React/Ink UI组件和布局信息
"""

import re
import json
import os
from collections import defaultdict

class DeepUIAnalyzer:
    def __init__(self, file_path):
        self.file_path = file_path
        self.content = ""
        self.results = {
            'react_components': [],
            'ink_components': [],
            'ui_functions': [],
            'color_theme': {},
            'layout_patterns': [],
            'state_management': [],
            'event_handlers': []
        }
        
    def load_file(self):
        """加载文件内容"""
        with open(self.file_path, 'r', encoding='utf-8', errors='ignore') as f:
            self.content = f.read()
        print(f"文件已加载，大小: {len(self.content):,} 字符")
        return True
        
    def extract_react_patterns(self):
        """提取React模式 - 基于已知的混淆模式"""
        patterns = {
            # React.createElement的各种混淆形式
            'createElement_patterns': [
                r'([a-zA-Z_]\w{0,2})\.createElement\s*\(',  # xx.createElement(
                r'createElement\s*\(\s*([a-zA-Z_]\w{0,2})',  # createElement(Component
                r'([a-zA-Z_]\w{0,2})\.default\.createElement\s*\(',  # xx.default.createElement(
            ],
            # Hooks模式
            'hooks_patterns': [
                r'useState\s*\(',
                r'useEffect\s*\(',
                r'useRef\s*\(',
                r'useMemo\s*\(',
                r'useCallback\s*\(',
                r'([a-zA-Z_]\w{0,2})\.useState\s*\(',  # xx.useState(
            ],
            # 组件定义模式
            'component_patterns': [
                r'function\s+([A-Z][a-zA-Z0-9]{1,3})\s*\([^)]*\)\s*\{[^}]*createElement',
                r'([a-zA-Z_]\w{0,2})\s*=\s*function[^{]*\{[^}]*createElement',
                r'const\s+([A-Z][a-zA-Z0-9]{1,3})\s*=\s*\([^)]*\)\s*=>\s*[^{]*createElement',
            ]
        }
        
        for category, pattern_list in patterns.items():
            for pattern in pattern_list:
                matches = re.findall(pattern, self.content[:1000000], re.IGNORECASE)
                if matches:
                    print(f"找到 {len(matches)} 个 {category} 匹配")
                    self.results[category] = self.results.get(category, [])
                    self.results[category].extend(matches[:20])  # 保存前20个
                    
    def find_ink_components(self):
        """查找Ink终端UI组件"""
        # Ink组件通常包括Box, Text, Static等
        ink_patterns = [
            r'Box[,\s\)]',  # Box组件
            r'Text[,\s\)]',  # Text组件
            r'Static[,\s\)]',  # Static组件
            r'Transform[,\s\)]',  # Transform组件
            r'Newline[,\s\)]',  # Newline组件
            r'Spacer[,\s\)]',  # Spacer组件
            r'render\s*\(',  # render函数
            r'useInput\s*\(',  # useInput hook
            r'useApp\s*\(',  # useApp hook
        ]
        
        for pattern in ink_patterns:
            matches = re.findall(pattern, self.content[:500000])
            if matches:
                print(f"找到Ink组件模式 {pattern}: {len(matches)} 次")
                self.results['ink_components'].append({
                    'pattern': pattern,
                    'count': len(matches)
                })
                
    def extract_color_and_style(self):
        """提取颜色和样式信息"""
        # 查找颜色定义
        color_patterns = [
            r'color\s*[:=]\s*["\']([^"\']+)["\']',
            r'backgroundColor\s*[:=]\s*["\']([^"\']+)["\']',
            r'borderColor\s*[:=]\s*["\']([^"\']+)["\']',
        ]
        
        colors = set()
        for pattern in color_patterns:
            matches = re.findall(pattern, self.content[:500000], re.IGNORECASE)
            colors.update(matches)
            
        # 查找常见的颜色值
        hex_colors = re.findall(r'#[0-9A-Fa-f]{3,6}\b', self.content[:500000])
        colors.update(hex_colors)
        
        # 基于分析项目的已知颜色
        known_colors = ['claude', 'success', 'error', 'warning', 'remember', 
                       'bashBorder', 'secondaryBorder', 'text', 'permission']
        
        found_colors = {}
        for color in known_colors:
            if color in self.content:
                found_colors[color] = True
                # 尝试找到颜色值
                pattern = f'{color}["\']?\s*[:=]\s*["\']([^"\']+)["\']'
                match = re.search(pattern, self.content[:500000])
                if match:
                    found_colors[color] = match.group(1)
                    
        self.results['color_theme'] = {
            'defined_colors': list(colors)[:20],
            'hex_colors': list(set(hex_colors))[:20],
            'known_theme_colors': found_colors
        }
        
    def find_layout_patterns(self):
        """查找布局模式"""
        layout_props = [
            'flexDirection', 'gap', 'padding', 'margin', 'border',
            'borderStyle', 'width', 'height', 'alignItems', 'justifyContent'
        ]
        
        for prop in layout_props:
            # 查找属性使用
            pattern = f'{prop}\\s*[:=]\\s*["\']?([^,;\\s\\}}"\')]+)'
            matches = re.findall(pattern, self.content[:500000], re.IGNORECASE)
            if matches:
                unique_values = list(set(matches))[:10]
                self.results['layout_patterns'].append({
                    'property': prop,
                    'values': unique_values,
                    'count': len(matches)
                })
                
    def find_state_management(self):
        """查找状态管理模式"""
        # 查找useState调用
        state_pattern = r'\[([a-zA-Z_]\w{0,2}),\s*([a-zA-Z_]\w{0,2})\]\s*=\s*[^.]+\.useState\s*\('
        matches = re.findall(state_pattern, self.content[:500000])
        
        for state, setState in matches[:20]:
            self.results['state_management'].append({
                'state': state,
                'setState': setState
            })
            
        # 查找具体的状态初始值
        init_pattern = r'useState\s*\(\s*([^)]+)\s*\)'
        init_matches = re.findall(init_pattern, self.content[:200000])
        if init_matches:
            self.results['state_initial_values'] = init_matches[:20]
            
    def find_ui_functions(self):
        """查找UI相关函数 - 基于已知的混淆函数名"""
        # 基于分析项目的已知函数
        known_functions = {
            'y2A': '欢迎界面渲染器',
            'k2A': 'GA统计展示',
            'Wy2': '选择变化监听器', 
            'xy2': '信任确认对话框',
            'c9': '终端尺寸管理器',
            'Je0': 'IDE安装成功显示',
            'Z0': '全局键盘监听器',
            'Fq5': '进度条组件',
            'j2A': '工具统计图表'
        }
        
        for func_name, description in known_functions.items():
            if func_name in self.content:
                # 尝试找到函数定义
                pattern = f'function\\s+{func_name}\\s*\\([^)]*\\)'
                match = re.search(pattern, self.content)
                if match:
                    self.results['ui_functions'].append({
                        'name': func_name,
                        'description': description,
                        'found': True
                    })
                    
    def generate_detailed_report(self):
        """生成详细报告"""
        report_path = os.path.join(os.path.dirname(self.file_path), 'fenx', 'detailed_ui_analysis.md')
        
        with open(report_path, 'w', encoding='utf-8') as f:
            f.write("# Claude Code UI深度分析报告\n\n")
            
            # React组件分析
            f.write("## React组件分析\n\n")
            if 'createElement_patterns' in self.results:
                f.write("### createElement调用\n")
                f.write(f"找到 {len(self.results['createElement_patterns'])} 个createElement模式\n\n")
                
            if 'hooks_patterns' in self.results:
                f.write("### React Hooks使用\n")
                f.write(f"找到 {len(self.results['hooks_patterns'])} 个Hooks调用\n\n")
                
            # Ink组件
            f.write("## Ink终端UI组件\n\n")
            for comp in self.results['ink_components']:
                f.write(f"- {comp['pattern']}: {comp['count']} 次\n")
                
            # 颜色主题
            f.write("\n## 颜色主题\n\n")
            if self.results['color_theme']['known_theme_colors']:
                f.write("### 已知主题颜色\n")
                for color, value in self.results['color_theme']['known_theme_colors'].items():
                    f.write(f"- {color}: {value}\n")
                    
            # 布局模式
            f.write("\n## 布局模式\n\n")
            for layout in self.results['layout_patterns']:
                f.write(f"### {layout['property']}\n")
                f.write(f"- 使用次数: {layout['count']}\n")
                f.write(f"- 值: {', '.join(layout['values'][:5])}\n\n")
                
            # 状态管理
            f.write("## 状态管理\n\n")
            if self.results['state_management']:
                f.write("### useState模式\n")
                for state in self.results['state_management'][:10]:
                    f.write(f"- [{state['state']}, {state['setState']}]\n")
                    
            # UI函数
            f.write("\n## 已知UI函数\n\n")
            for func in self.results['ui_functions']:
                if func['found']:
                    f.write(f"- **{func['name']}**: {func['description']} ✓\n")
                    
        print(f"详细报告已生成: {report_path}")
        
        # 保存JSON格式
        json_path = os.path.join(os.path.dirname(self.file_path), 'fenx', 'detailed_analysis.json')
        with open(json_path, 'w', encoding='utf-8') as f:
            json.dump(self.results, f, indent=2, ensure_ascii=False)

def main():
    analyzer = DeepUIAnalyzer(r"E:\claude\yuan\package\cli.js")
    
    print("开始深度UI分析...")
    analyzer.load_file()
    
    print("\n提取React模式...")
    analyzer.extract_react_patterns()
    
    print("\n查找Ink组件...")
    analyzer.find_ink_components()
    
    print("\n提取颜色和样式...")
    analyzer.extract_color_and_style()
    
    print("\n查找布局模式...")
    analyzer.find_layout_patterns()
    
    print("\n分析状态管理...")
    analyzer.find_state_management()
    
    print("\n查找已知UI函数...")
    analyzer.find_ui_functions()
    
    print("\n生成报告...")
    analyzer.generate_detailed_report()
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()