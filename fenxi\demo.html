<!DOCTYPE html>
<html lang="zh-CN">
<head>
    <meta charset="UTF-8">
    <meta name="viewport" content="width=device-width, initial-scale=1.0">
    <title>Claude Code UI组件演示</title>
    <script src="https://unpkg.com/react@18/umd/react.development.js"></script>
    <script src="https://unpkg.com/react-dom@18/umd/react-dom.development.js"></script>
    <script src="https://unpkg.com/@babel/standalone/babel.min.js"></script>
    <style>
        body {
            margin: 0;
            padding: 0;
            font-family: system-ui, -apple-system, BlinkMacSystemFont, 'Segoe UI', sans-serif;
            background-color: #1a1a1a;
            color: #ffffff;
        }
        
        .demo-container {
            max-width: 1200px;
            margin: 0 auto;
            padding: 20px;
        }
        
        .demo-section {
            margin-bottom: 40px;
            padding: 20px;
            background-color: #2a2a2a;
            border-radius: 8px;
            border: 1px solid #444;
        }
        
        .demo-title {
            font-size: 24px;
            font-weight: bold;
            margin-bottom: 16px;
            color: #ee78e6;
        }
        
        .demo-description {
            margin-bottom: 20px;
            color: #a0a0a0;
            line-height: 1.5;
        }
        
        .component-grid {
            display: grid;
            grid-template-columns: repeat(auto-fit, minmax(300px, 1fr));
            gap: 20px;
        }
        
        .component-demo {
            padding: 16px;
            background-color: #333740;
            border-radius: 6px;
            border: 1px solid #555;
        }
        
        .component-name {
            font-weight: bold;
            margin-bottom: 12px;
            color: #4dabf7;
        }
        
        pre {
            background-color: #1a1a1a;
            padding: 12px;
            border-radius: 4px;
            overflow-x: auto;
            font-size: 14px;
            border: 1px solid #444;
        }
        
        code {
            color: #69db7c;
        }
    </style>
</head>
<body>
    <div class="demo-container">
        <h1 style="text-align: center; color: #ee78e6; margin-bottom: 40px;">
            ✻ Claude Code UI组件演示
        </h1>
        
        <div class="demo-section">
            <div class="demo-title">项目概述</div>
            <div class="demo-description">
                本演示展示了从Claude Code混淆代码中提取和重构的UI组件。通过深度分析，我们成功识别了：
                <ul>
                    <li><strong>1,031个重构组件</strong> - 从混淆代码中提取的React组件</li>
                    <li><strong>28种颜色系统</strong> - 包含语义化颜色和品牌色</li>
                    <li><strong>22种样式属性</strong> - 布局、颜色、字体等核心样式</li>
                    <li><strong>完整的设计系统</strong> - 可复用的组件和样式规范</li>
                </ul>
            </div>
        </div>

        <div class="demo-section">
            <div class="demo-title">颜色系统</div>
            <div class="demo-description">
                从分析中提取的Claude Code颜色系统，包含语义化颜色和品牌识别色。
            </div>
            <div id="color-demo"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">基础组件</div>
            <div class="demo-description">
                重构的基础UI组件，包含文本、容器、按钮等常用元素。
            </div>
            <div id="basic-components-demo"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">消息组件</div>
            <div class="demo-description">
                用于显示不同状态消息的组件，支持错误、成功、警告等类型。
            </div>
            <div id="message-components-demo"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">复合组件</div>
            <div class="demo-description">
                基于基础组件构建的复杂界面组件，展示Claude Code的实际界面效果。
            </div>
            <div id="complex-components-demo"></div>
        </div>

        <div class="demo-section">
            <div class="demo-title">使用说明</div>
            <div class="demo-description">
                <h3>如何使用这些组件：</h3>
                <pre><code>// 1. 导入组件
import { Text, Box, Button, ClaudeBrand } from './ClaudeCodeUIComponents.jsx';

// 2. 使用组件
function MyApp() {
  return (
    &lt;Box flexDirection="column" padding={2}&gt;
      &lt;ClaudeBrand&gt;Claude Code&lt;/ClaudeBrand&gt;
      &lt;Text color="secondaryText"&gt;Welcome to Claude Code&lt;/Text&gt;
      &lt;Button onClick={() =&gt; console.log('Hello!')}&gt;
        Get Started
      &lt;/Button&gt;
    &lt;/Box&gt;
  );
}</code></pre>
                
                <h3>文件结构：</h3>
                <ul>
                    <li><code>ClaudeCodeUIComponents.jsx</code> - 重构的UI组件库</li>
                    <li><code>ComponentUsageExample.jsx</code> - 完整的使用示例</li>
                    <li><code>reconstructed-components.json</code> - 原始分析数据</li>
                    <li><code>reconstructed-styles.css</code> - 提取的CSS样式</li>
                </ul>
            </div>
        </div>
    </div>

    <script type="text/babel">
        const { useState } = React;

        // 颜色系统演示
        const ColorDemo = () => {
            const colors = {
                error: '#ff6b6b',
                success: '#51cf66',
                warning: '#ffd43b',
                claude: '#ee78e6',
                ide: '#4dabf7',
                suggestion: '#69db7c',
                text: '#ffffff',
                secondaryText: '#a0a0a0'
            };

            return (
                <div className="component-grid">
                    {Object.entries(colors).map(([name, color]) => (
                        <div key={name} className="component-demo">
                            <div className="component-name">{name}</div>
                            <div style={{
                                width: '100%',
                                height: '40px',
                                backgroundColor: color,
                                borderRadius: '4px',
                                marginBottom: '8px'
                            }}></div>
                            <code>{color}</code>
                        </div>
                    ))}
                </div>
            );
        };

        // 基础组件演示
        const BasicComponentsDemo = () => {
            return (
                <div className="component-grid">
                    <div className="component-demo">
                        <div className="component-name">Text组件</div>
                        <div style={{ marginBottom: '8px' }}>
                            <span style={{ color: '#ffffff' }}>普通文本</span>
                        </div>
                        <div style={{ marginBottom: '8px' }}>
                            <span style={{ color: '#ee78e6', fontWeight: 'bold' }}>Claude品牌色文本</span>
                        </div>
                        <div>
                            <span style={{ color: '#a0a0a0', opacity: 0.6 }}>次要文本</span>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">Button组件</div>
                        <div style={{ display: 'flex', gap: '8px', flexWrap: 'wrap' }}>
                            <button style={{
                                padding: '8px 16px',
                                borderRadius: '4px',
                                border: 'none',
                                backgroundColor: '#ee78e6',
                                color: '#ffffff',
                                fontWeight: 'bold',
                                cursor: 'pointer'
                            }}>Primary</button>
                            <button style={{
                                padding: '8px 16px',
                                borderRadius: '4px',
                                border: 'none',
                                backgroundColor: '#51cf66',
                                color: '#ffffff',
                                fontWeight: 'bold',
                                cursor: 'pointer'
                            }}>Success</button>
                            <button style={{
                                padding: '8px 16px',
                                borderRadius: '4px',
                                border: '1px solid #a0a0a0',
                                backgroundColor: 'transparent',
                                color: '#a0a0a0',
                                fontWeight: 'bold',
                                cursor: 'pointer'
                            }}>Secondary</button>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">Input组件</div>
                        <input style={{
                            width: '100%',
                            padding: '8px 12px',
                            borderRadius: '4px',
                            border: '1px solid #a0a0a0',
                            backgroundColor: '#333740',
                            color: '#ffffff',
                            fontSize: '14px',
                            outline: 'none'
                        }} placeholder="输入文本..." />
                    </div>
                </div>
            );
        };

        // 消息组件演示
        const MessageComponentsDemo = () => {
            return (
                <div className="component-grid">
                    <div className="component-demo">
                        <div className="component-name">成功消息</div>
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#333740',
                            borderRadius: '4px',
                            border: '1px solid #51cf66'
                        }}>
                            <div style={{ color: '#51cf66', fontWeight: 'bold', marginBottom: '4px' }}>
                                ✓ SUCCESS
                            </div>
                            <div style={{ color: '#ffffff' }}>
                                操作已成功完成
                            </div>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">错误消息</div>
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#333740',
                            borderRadius: '4px',
                            border: '1px solid #ff6b6b'
                        }}>
                            <div style={{ color: '#ff6b6b', fontWeight: 'bold', marginBottom: '4px' }}>
                                ✗ ERROR
                            </div>
                            <div style={{ color: '#ffffff' }}>
                                发生了一个错误
                            </div>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">警告消息</div>
                        <div style={{
                            padding: '12px',
                            backgroundColor: '#333740',
                            borderRadius: '4px',
                            border: '1px solid #ffd43b'
                        }}>
                            <div style={{ color: '#ffd43b', fontWeight: 'bold', marginBottom: '4px' }}>
                                ⚠ WARNING
                            </div>
                            <div style={{ color: '#ffffff' }}>
                                请注意这个警告
                            </div>
                        </div>
                    </div>
                </div>
            );
        };

        // 复合组件演示
        const ComplexComponentsDemo = () => {
            return (
                <div className="component-grid">
                    <div className="component-demo">
                        <div className="component-name">欢迎界面</div>
                        <div style={{
                            padding: '16px',
                            border: '1px solid #4dabf7',
                            borderRadius: '8px',
                            backgroundColor: '#333740'
                        }}>
                            <div style={{ marginBottom: '12px' }}>
                                <span style={{ color: '#ee78e6', fontWeight: 'bold' }}>✻ Claude Code</span>
                            </div>
                            <div style={{ marginBottom: '8px' }}>
                                <span style={{ color: '#ffffff' }}>Welcome to </span>
                                <span style={{ color: '#ffffff', fontWeight: 'bold' }}>Claude Code</span>
                            </div>
                            <div style={{ color: '#a0a0a0' }}>
                                installed v1.0.61
                            </div>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">状态指示器</div>
                        <div style={{ display: 'flex', flexDirection: 'column', gap: '8px' }}>
                            <div style={{ color: '#4dabf7' }}>● IDE connected</div>
                            <div style={{ color: '#ff6b6b' }}>● IDE disconnected</div>
                            <div style={{ color: '#51cf66' }}>✓ Update installed</div>
                            <div style={{ color: '#ffd43b' }}>⚠ Debug mode</div>
                        </div>
                    </div>
                    
                    <div className="component-demo">
                        <div className="component-name">进度条</div>
                        <div style={{ marginBottom: '8px', color: '#a0a0a0' }}>Processing...</div>
                        <div style={{
                            width: '100%',
                            height: '4px',
                            backgroundColor: '#444',
                            borderRadius: '2px',
                            overflow: 'hidden'
                        }}>
                            <div style={{
                                width: '65%',
                                height: '100%',
                                backgroundColor: '#ee78e6',
                                transition: 'width 0.3s ease'
                            }}></div>
                        </div>
                    </div>
                </div>
            );
        };

        // 渲染所有演示组件
        ReactDOM.render(<ColorDemo />, document.getElementById('color-demo'));
        ReactDOM.render(<BasicComponentsDemo />, document.getElementById('basic-components-demo'));
        ReactDOM.render(<MessageComponentsDemo />, document.getElementById('message-components-demo'));
        ReactDOM.render(<ComplexComponentsDemo />, document.getElementById('complex-components-demo'));
    </script>
</body>
</html>
