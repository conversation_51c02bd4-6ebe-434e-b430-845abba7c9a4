#!/usr/bin/env node

/**
 * Claude Code UI组件重构工具
 * 尝试从混淆代码中重构出可读的UI组件代码
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class ComponentReconstructor {
    constructor() {
        // 组件重构模式
        this.patterns = {
            // React createElement 完整模式
            createElementFull: /createElement\s*\(\s*["']?(\w+)["']?\s*,\s*\{([^}]*)\}\s*,\s*([^)]*)\)/gi,
            
            // 样式对象模式
            styleObject: /\{([^}]*(?:color|backgroundColor|fontSize|padding|margin|width|height)[^}]*)\}/gi,
            
            // 函数组件模式
            functionComponent: /function\s+(\w+)\s*\([^)]*\)\s*\{([^}]*createElement[^}]*)\}/gi,
            
            // 箭头函数组件模式
            arrowComponent: /(\w+)\s*=\s*\([^)]*\)\s*=>\s*\{?([^}]*createElement[^}]*)\}?/gi,
            
            // 条件渲染模式
            conditionalRender: /([\w.]+)\s*\?\s*createElement\([^)]+\)\s*:\s*createElement\([^)]+\)/gi,
            
            // 事件处理器模式
            eventHandler: /(on\w+):\s*([^,}]+)/gi,
            
            // 样式计算函数
            styleFunction: /(\w+):\s*\([^)]*\)\s*=>\s*\(\{([^}]+)\}\)/gi,
        };
        
        // 常见的React组件属性映射
        this.propMappings = {
            'backgroundColor': 'backgroundColor',
            'color': 'color',
            'fontSize': 'fontSize',
            'fontWeight': 'fontWeight',
            'padding': 'padding',
            'margin': 'margin',
            'width': 'width',
            'height': 'height',
            'display': 'display',
            'flexDirection': 'flexDirection',
            'alignItems': 'alignItems',
            'justifyContent': 'justifyContent',
            'borderRadius': 'borderRadius',
            'border': 'border',
            'opacity': 'opacity',
            'transform': 'transform',
            'transition': 'transition'
        };
        
        // 组件模板
        this.templates = {
            functionComponent: (name, props, children, styles) => `
function ${name}(${props}) {
  const styles = ${JSON.stringify(styles, null, 2)};
  
  return React.createElement(
    "${name.toLowerCase()}",
    { style: styles${props ? `, ...props` : ''} },
    ${children || 'null'}
  );
}`,
            
            arrowComponent: (name, props, children, styles) => `
const ${name} = (${props}) => {
  const styles = ${JSON.stringify(styles, null, 2)};
  
  return React.createElement(
    "${name.toLowerCase()}",
    { style: styles${props ? `, ...props` : ''} },
    ${children || 'null'}
  );
};`,
            
            styledComponent: (name, baseComponent, styles) => `
const ${name} = styled.${baseComponent}\`
${this.convertToCSS(styles)}
\`;`
        };
    }

    /**
     * 重构组件代码
     */
    reconstructComponents(filePath) {
        console.log(`\n🔧 重构UI组件: ${path.basename(filePath)}`);
        
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return null;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const results = {
            file: filePath,
            reconstructedComponents: [],
            styleObjects: [],
            eventHandlers: [],
            conditionalRenders: [],
            statistics: {
                totalComponents: 0,
                totalStyles: 0,
                totalEvents: 0
            }
        };

        // 提取和重构组件
        this.extractCreateElements(content, results);
        this.extractStyleObjects(content, results);
        this.extractEventHandlers(content, results);
        this.extractConditionalRenders(content, results);
        this.generateReconstructedCode(results);

        return results;
    }

    /**
     * 提取createElement调用
     */
    extractCreateElements(content, results) {
        let match;
        while ((match = this.patterns.createElementFull.exec(content)) !== null) {
            const elementType = match[1];
            const propsString = match[2];
            const children = match[3];
            
            // 解析属性
            const props = this.parseProps(propsString);
            const styles = this.extractStylesFromProps(props);
            
            const component = {
                type: 'createElement',
                elementType: elementType,
                props: props,
                styles: styles,
                children: children.trim(),
                context: this.getContext(content, match.index, 200),
                reconstructed: this.reconstructComponent(elementType, props, children, styles)
            };
            
            results.reconstructedComponents.push(component);
        }
    }

    /**
     * 提取样式对象
     */
    extractStyleObjects(content, results) {
        let match;
        while ((match = this.patterns.styleObject.exec(content)) !== null) {
            const styleContent = match[1];
            const parsedStyles = this.parseStyleObject(styleContent);
            
            if (Object.keys(parsedStyles).length > 0) {
                results.styleObjects.push({
                    raw: styleContent,
                    parsed: parsedStyles,
                    css: this.convertToCSS(parsedStyles),
                    context: this.getContext(content, match.index, 100)
                });
            }
        }
    }

    /**
     * 提取事件处理器
     */
    extractEventHandlers(content, results) {
        let match;
        while ((match = this.patterns.eventHandler.exec(content)) !== null) {
            results.eventHandlers.push({
                event: match[1],
                handler: match[2],
                context: this.getContext(content, match.index, 80)
            });
        }
    }

    /**
     * 提取条件渲染
     */
    extractConditionalRenders(content, results) {
        let match;
        while ((match = this.patterns.conditionalRender.exec(content)) !== null) {
            results.conditionalRenders.push({
                condition: match[1],
                render: match[0],
                context: this.getContext(content, match.index, 150)
            });
        }
    }

    /**
     * 解析属性字符串
     */
    parseProps(propsString) {
        const props = {};
        
        // 简单的属性解析（可能需要更复杂的解析器）
        const propMatches = propsString.match(/(\w+):\s*([^,}]+)/g);
        if (propMatches) {
            propMatches.forEach(propMatch => {
                const [, key, value] = propMatch.match(/(\w+):\s*(.+)/);
                props[key] = value.replace(/['"]/g, '');
            });
        }
        
        return props;
    }

    /**
     * 从属性中提取样式
     */
    extractStylesFromProps(props) {
        const styles = {};
        
        Object.keys(props).forEach(key => {
            if (this.propMappings[key]) {
                styles[this.propMappings[key]] = props[key];
            }
        });
        
        return styles;
    }

    /**
     * 解析样式对象
     */
    parseStyleObject(styleContent) {
        const styles = {};
        
        // 匹配样式属性
        const styleMatches = styleContent.match(/(\w+):\s*["']?([^,"'}]+)["']?/g);
        if (styleMatches) {
            styleMatches.forEach(styleMatch => {
                const [, property, value] = styleMatch.match(/(\w+):\s*["']?([^,"'}]+)["']?/);
                if (this.propMappings[property]) {
                    styles[property] = value;
                }
            });
        }
        
        return styles;
    }

    /**
     * 将样式对象转换为CSS
     */
    convertToCSS(styles) {
        return Object.entries(styles)
            .map(([property, value]) => {
                // 将驼峰命名转换为短横线命名
                const cssProperty = property.replace(/([A-Z])/g, '-$1').toLowerCase();
                return `  ${cssProperty}: ${value};`;
            })
            .join('\n');
    }

    /**
     * 重构组件代码
     */
    reconstructComponent(elementType, props, children, styles) {
        const componentName = elementType.charAt(0).toUpperCase() + elementType.slice(1) + 'Component';
        const propsParam = Object.keys(props).length > 0 ? 'props' : '';
        
        return this.templates.functionComponent(componentName, propsParam, children, styles);
    }

    /**
     * 生成重构后的代码
     */
    generateReconstructedCode(results) {
        results.statistics.totalComponents = results.reconstructedComponents.length;
        results.statistics.totalStyles = results.styleObjects.length;
        results.statistics.totalEvents = results.eventHandlers.length;
        
        // 生成完整的组件文件
        results.fullReconstructedFile = this.generateFullComponentFile(results);
    }

    /**
     * 生成完整的组件文件
     */
    generateFullComponentFile(results) {
        let code = `// 重构的Claude Code UI组件
import React from 'react';

// 样式常量
const styles = {
${results.styleObjects.map((style, index) => 
    `  style${index}: ${JSON.stringify(style.parsed, null, 4)}`
).join(',\n')}
};

// 重构的组件
${results.reconstructedComponents.map(comp => comp.reconstructed).join('\n\n')}

export default {
${results.reconstructedComponents.map((comp, index) => 
    `  Component${index}: ${comp.elementType}Component`
).join(',\n')}
};`;

        return code;
    }

    /**
     * 获取上下文
     */
    getContext(content, index, length = 100) {
        const start = Math.max(0, index - length);
        const end = Math.min(content.length, index + length);
        return content.substring(start, end).replace(/\n/g, '\\n');
    }

    /**
     * 生成报告
     */
    generateReport(results) {
        if (!results) return;

        console.log(`\n📊 组件重构报告`);
        console.log(`文件: ${path.basename(results.file)}`);
        console.log(`重构组件数: ${results.statistics.totalComponents}`);
        console.log(`样式对象数: ${results.statistics.totalStyles}`);
        console.log(`事件处理器数: ${results.statistics.totalEvents}`);

        if (results.reconstructedComponents.length > 0) {
            console.log(`\n🧩 重构的组件:`);
            results.reconstructedComponents.slice(0, 5).forEach((comp, index) => {
                console.log(`\n${index + 1}. ${comp.elementType} 组件:`);
                console.log(`   属性: ${Object.keys(comp.props).join(', ')}`);
                console.log(`   样式: ${Object.keys(comp.styles).join(', ')}`);
                if (comp.children) {
                    console.log(`   子元素: ${comp.children.substring(0, 50)}...`);
                }
            });
        }

        if (results.styleObjects.length > 0) {
            console.log(`\n🎨 发现的样式对象:`);
            results.styleObjects.slice(0, 3).forEach((style, index) => {
                console.log(`\n${index + 1}. 样式对象:`);
                console.log(`   CSS:\n${style.css}`);
            });
        }
    }

    /**
     * 保存重构结果
     */
    saveResults(results, outputDir) {
        // 保存JSON数据
        const jsonPath = path.join(outputDir, 'reconstructed-components.json');
        fs.writeFileSync(jsonPath, JSON.stringify(results, null, 2), 'utf8');
        
        // 保存重构的组件代码
        const codePath = path.join(outputDir, 'ReconstructedComponents.jsx');
        fs.writeFileSync(codePath, results.fullReconstructedFile, 'utf8');
        
        // 保存样式文件
        const cssPath = path.join(outputDir, 'reconstructed-styles.css');
        const cssContent = results.styleObjects.map((style, index) => 
            `.style-${index} {\n${style.css}\n}`
        ).join('\n\n');
        fs.writeFileSync(cssPath, cssContent, 'utf8');

        console.log(`\n💾 重构结果已保存:`);
        console.log(`   JSON数据: ${jsonPath}`);
        console.log(`   React组件: ${codePath}`);
        console.log(`   CSS样式: ${cssPath}`);
    }
}

// 主函数
function main() {
    console.log('🔧 Claude Code UI组件重构工具');
    console.log('===============================');

    const reconstructor = new ComponentReconstructor();
    const cliPath = path.join(__dirname, '..', 'cli.js');

    if (fs.existsSync(cliPath)) {
        const results = reconstructor.reconstructComponents(cliPath);
        if (results) {
            reconstructor.generateReport(results);
            reconstructor.saveResults(results, __dirname);
        }
    } else {
        console.log(`⚠️ 文件不存在: ${cliPath}`);
    }

    console.log('\n✅ 组件重构完成!');
}

// 运行主函数
main();

export default ComponentReconstructor;
