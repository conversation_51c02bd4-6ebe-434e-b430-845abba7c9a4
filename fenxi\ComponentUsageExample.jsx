/**
 * Claude Code UI组件使用示例
 * 展示如何使用重构的组件来复现Claude Code的界面
 */

import React, { useState } from 'react';
import {
  Text,
  Box,
  ErrorMessage,
  SuccessMessage,
  WarningMessage,
  ClaudeBrand,
  ConfigurationError,
  WelcomeScreen,
  StatusIndicator,
  ProgressBar,
  CodeBlock,
  Button,
  Input,
  Select,
  Checkbox,
  colors
} from './ClaudeCodeUIComponents.jsx';

// 主应用组件示例
const ClaudeCodeApp = () => {
  const [currentView, setCurrentView] = useState('welcome');
  const [progress, setProgress] = useState(0);
  const [inputValue, setInputValue] = useState('');
  const [selectedModel, setSelectedModel] = useState('claude-3-sonnet');
  const [isConnected, setIsConnected] = useState(false);

  // 模拟进度更新
  React.useEffect(() => {
    const interval = setInterval(() => {
      setProgress(prev => (prev >= 100 ? 0 : prev + 10));
    }, 1000);
    return () => clearInterval(interval);
  }, []);

  const modelOptions = [
    { label: 'Claude 3 Sonnet', value: 'claude-3-sonnet' },
    { label: 'Claude 3 Opus', value: 'claude-3-opus' },
    { label: 'Claude 3 Haiku', value: 'claude-3-haiku' }
  ];

  const renderWelcomeView = () => (
    <Box flexDirection="column" style={{ gap: '16px', maxWidth: '600px' }}>
      <WelcomeScreen version="1.0.61" />
      
      <Box flexDirection="column" style={{ gap: '8px' }}>
        <Text color="secondaryText">Tips for getting started:</Text>
        <Text color="secondaryText">• Ask Claude to create a new app or clone a repository</Text>
        <Text color="secondaryText">• Run /init to create a CLAUDE.md file with instructions for Claude</Text>
        <Text color="secondaryText">• Run /terminal-setup to set up terminal integration</Text>
        <Text color="secondaryText">• Use Claude to help with file analysis, editing, bash commands and git</Text>
        <Text color="secondaryText">• Be as specific as you would with another engineer for the best results</Text>
      </Box>

      <Box flexDirection="row" style={{ gap: '8px' }}>
        <Button onClick={() => setCurrentView('chat')}>
          Start Chatting
        </Button>
        <Button variant="secondary" onClick={() => setCurrentView('config')}>
          Configuration
        </Button>
      </Box>
    </Box>
  );

  const renderChatView = () => (
    <Box flexDirection="column" style={{ gap: '16px', maxWidth: '800px' }}>
      {/* 状态栏 */}
      <Box flexDirection="row" style={{ justifyContent: 'space-between', alignItems: 'center' }}>
        <Box flexDirection="row" style={{ gap: '16px' }}>
          <StatusIndicator status={isConnected ? 'connected' : 'error'}>
            {isConnected ? 'IDE connected' : 'IDE disconnected'}
          </StatusIndicator>
          <Text color="secondaryText">{progress} tokens</Text>
        </Box>
        <Button variant="secondary" onClick={() => setCurrentView('welcome')}>
          Back
        </Button>
      </Box>

      {/* 模型选择 */}
      <Box flexDirection="row" style={{ alignItems: 'center', gap: '8px' }}>
        <Text>Model:</Text>
        <Select 
          options={modelOptions}
          value={selectedModel}
          onChange={(e) => setSelectedModel(e.target.value)}
        />
      </Box>

      {/* 进度条 */}
      <Box flexDirection="column" style={{ gap: '4px' }}>
        <Text color="secondaryText">Processing...</Text>
        <ProgressBar progress={progress} width={300} />
      </Box>

      {/* 消息示例 */}
      <Box flexDirection="column" style={{ gap: '12px' }}>
        <SuccessMessage>
          ✓ File successfully created: src/components/App.jsx
        </SuccessMessage>
        
        <WarningMessage>
          Approaching usage limit • resets at 2024-01-28 15:00
        </WarningMessage>
        
        <Box flexDirection="column" style={{ gap: '8px' }}>
          <ClaudeBrand>Claude</ClaudeBrand>
          <Text>I'll help you create a React component. Here's the code:</Text>
          <CodeBlock language="jsx">
{`function MyComponent({ title }) {
  return (
    <div className="component">
      <h1>{title}</h1>
    </div>
  );
}`}
          </CodeBlock>
        </Box>
      </Box>

      {/* 输入区域 */}
      <Box flexDirection="column" style={{ gap: '8px' }}>
        <Text>Enter your message:</Text>
        <Input
          value={inputValue}
          onChange={(e) => setInputValue(e.target.value)}
          placeholder="Ask Claude anything..."
          style={{ width: '100%' }}
        />
        <Box flexDirection="row" style={{ gap: '8px' }}>
          <Button onClick={() => console.log('Send:', inputValue)}>
            Send
          </Button>
          <Button variant="secondary" onClick={() => setInputValue('')}>
            Clear
          </Button>
        </Box>
      </Box>

      {/* 快捷提示 */}
      <Box flexDirection="row" style={{ gap: '16px', flexWrap: 'wrap' }}>
        <Text color="secondaryText">! for bash mode</Text>
        <Text color="secondaryText">/ for commands</Text>
        <Text color="secondaryText">@ for file paths</Text>
        <Text color="secondaryText"># to memorize</Text>
      </Box>
    </Box>
  );

  const renderConfigView = () => (
    <Box flexDirection="column" style={{ gap: '16px', maxWidth: '500px' }}>
      <Text bold style={{ fontSize: '18px' }}>Configuration</Text>
      
      <Box flexDirection="column" style={{ gap: '12px' }}>
        <Box flexDirection="row" style={{ alignItems: 'center', gap: '8px' }}>
          <Checkbox 
            checked={isConnected}
            onChange={() => setIsConnected(!isConnected)}
          >
            Enable IDE integration
          </Checkbox>
        </Box>
        
        <Box flexDirection="column" style={{ gap: '4px' }}>
          <Text>API Key:</Text>
          <Input
            type="password"
            placeholder="Enter your Anthropic API key..."
            style={{ width: '100%' }}
          />
        </Box>
        
        <Box flexDirection="column" style={{ gap: '4px' }}>
          <Text>Default Model:</Text>
          <Select 
            options={modelOptions}
            value={selectedModel}
            onChange={(e) => setSelectedModel(e.target.value)}
            style={{ width: '100%' }}
          />
        </Box>
      </Box>

      <Box flexDirection="row" style={{ gap: '8px' }}>
        <Button onClick={() => console.log('Save config')}>
          Save Configuration
        </Button>
        <Button variant="secondary" onClick={() => setCurrentView('welcome')}>
          Cancel
        </Button>
      </Box>
    </Box>
  );

  const renderErrorView = () => (
    <ConfigurationError
      filePath="/path/to/config.json"
      onReset={() => {
        console.log('Reset configuration');
        setCurrentView('welcome');
      }}
      onExit={() => {
        console.log('Exit application');
      }}
    />
  );

  return (
    <Box 
      style={{
        minHeight: '100vh',
        backgroundColor: '#1a1a1a',
        color: colors.text,
        padding: '20px',
        fontFamily: 'system-ui, -apple-system, sans-serif'
      }}
    >
      <Box flexDirection="column" style={{ alignItems: 'center' }}>
        {/* 导航栏 */}
        <Box 
          flexDirection="row" 
          style={{ 
            gap: '16px', 
            marginBottom: '32px',
            padding: '16px',
            backgroundColor: colors.backgroundColor,
            borderRadius: '8px',
            width: '100%',
            maxWidth: '800px',
            justifyContent: 'center'
          }}
        >
          <Button 
            variant={currentView === 'welcome' ? 'primary' : 'secondary'}
            onClick={() => setCurrentView('welcome')}
          >
            Welcome
          </Button>
          <Button 
            variant={currentView === 'chat' ? 'primary' : 'secondary'}
            onClick={() => setCurrentView('chat')}
          >
            Chat
          </Button>
          <Button 
            variant={currentView === 'config' ? 'primary' : 'secondary'}
            onClick={() => setCurrentView('config')}
          >
            Config
          </Button>
          <Button 
            variant={currentView === 'error' ? 'primary' : 'secondary'}
            onClick={() => setCurrentView('error')}
          >
            Error Demo
          </Button>
        </Box>

        {/* 主内容区域 */}
        <Box style={{ width: '100%', maxWidth: '800px' }}>
          {currentView === 'welcome' && renderWelcomeView()}
          {currentView === 'chat' && renderChatView()}
          {currentView === 'config' && renderConfigView()}
          {currentView === 'error' && renderErrorView()}
        </Box>
      </Box>
    </Box>
  );
};

export default ClaudeCodeApp;
