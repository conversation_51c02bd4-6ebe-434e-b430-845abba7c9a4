/**
 * <PERSON> Code UI组件复现示例
 * 基于从混淆代码中提取的信息重构的可用组件
 */

import React from 'react';

// 颜色主题定义（从分析中提取）
const colors = {
  // 语义化颜色
  error: '#ff6b6b',
  success: '#51cf66',
  warning: '#ffd43b',
  info: '#339af0',
  
  // 文本颜色
  text: '#ffffff',
  secondaryText: '#a0a0a0',
  
  // Claude品牌色
  claude: '#ee78e6',
  
  // IDE相关颜色
  ide: '#4dabf7',
  
  // 特殊颜色
  suggestion: '#69db7c',
  permission: '#ffd43b',
  remember: '#845ef7',
  bashBorder: '#fd7e14',
  
  // 差异显示颜色
  diffAddedWord: '#51cf66',
  diffRemovedWord: '#ff6b6b',
  
  // 背景色
  backgroundColor: '#333740',
  
  // 基础颜色
  black: '#000000',
  white: '#ffffff',
  green: '#51cf66',
  blue: '#339af0',
  purple: '#9775fa',
  teal: '#20c997'
};

// 基础样式系统
const baseStyles = {
  // 布局样式
  flexColumn: {
    display: 'flex',
    flexDirection: 'column'
  },
  
  flexRow: {
    display: 'flex',
    flexDirection: 'row'
  },
  
  // 间距系统
  padding1: { padding: '8px' },
  padding2: { padding: '16px' },
  margin1: { margin: '8px' },
  margin2: { margin: '16px' },
  
  // 文本样式
  bold: { fontWeight: 'bold' },
  dimColor: { opacity: 0.6 },
  
  // 边框样式
  roundBorder: {
    borderRadius: '4px',
    border: '1px solid #555'
  }
};

// 基础文本组件
export const Text = ({ 
  children, 
  color = 'text', 
  bold = false, 
  dimColor = false,
  style = {},
  ...props 
}) => {
  const textStyle = {
    color: colors[color] || color,
    fontWeight: bold ? 'bold' : 'normal',
    opacity: dimColor ? 0.6 : 1,
    ...style
  };
  
  return (
    <span style={textStyle} {...props}>
      {children}
    </span>
  );
};

// 容器组件
export const Box = ({ 
  children, 
  flexDirection = 'column',
  padding = 0,
  margin = 0,
  width,
  height,
  backgroundColor,
  style = {},
  ...props 
}) => {
  const boxStyle = {
    display: 'flex',
    flexDirection,
    padding: typeof padding === 'number' ? `${padding * 8}px` : padding,
    margin: typeof margin === 'number' ? `${margin * 8}px` : margin,
    width,
    height,
    backgroundColor: colors[backgroundColor] || backgroundColor,
    ...style
  };
  
  return (
    <div style={boxStyle} {...props}>
      {children}
    </div>
  );
};

// 错误消息组件
export const ErrorMessage = ({ children, ...props }) => (
  <Box flexDirection="column" padding={1} {...props}>
    <Text color="error" bold>
      ✗ ERROR
    </Text>
    <Text color="text">
      {children}
    </Text>
  </Box>
);

// 成功消息组件
export const SuccessMessage = ({ children, ...props }) => (
  <Box flexDirection="column" padding={1} {...props}>
    <Text color="success" bold>
      ✓ SUCCESS
    </Text>
    <Text color="text">
      {children}
    </Text>
  </Box>
);

// 警告消息组件
export const WarningMessage = ({ children, ...props }) => (
  <Box flexDirection="column" padding={1} {...props}>
    <Text color="warning" bold>
      ⚠ WARNING
    </Text>
    <Text color="text">
      {children}
    </Text>
  </Box>
);

// Claude品牌标识组件
export const ClaudeBrand = ({ children, ...props }) => (
  <Text color="claude" bold {...props}>
    ✻ {children}
  </Text>
);

// 配置错误组件（从分析中提取的实际组件）
export const ConfigurationError = ({ filePath, onReset, onExit }) => (
  <Box flexDirection="column" padding={1} width={70}>
    <Text bold>Configuration Error</Text>
    <Box flexDirection="column">
      <Text>
        The configuration file at <Text bold>{filePath}</Text> contains errors.
      </Text>
    </Box>
    <Box flexDirection="column">
      <Text bold>Choose an option:</Text>
      <Box flexDirection="column">
        <button onClick={onReset}>Reset with default configuration</button>
        <button onClick={onExit}>Exit</button>
      </Box>
    </Box>
  </Box>
);

// 欢迎界面组件
export const WelcomeScreen = ({ version }) => (
  <Box 
    flexDirection="column" 
    style={{
      borderStyle: 'solid',
      borderWidth: '1px',
      borderColor: colors.ide,
      borderRadius: '8px',
      padding: '16px',
      gap: '8px'
    }}
  >
    <Box>
      <ClaudeBrand>Claude Code</ClaudeBrand>
    </Box>
    <Box flexDirection="column">
      <Text>Welcome to <Text bold>Claude Code</Text></Text>
      <Text color="secondaryText">
        installed v{version}
      </Text>
    </Box>
  </Box>
);

// 状态指示器组件
export const StatusIndicator = ({ status, children }) => {
  const getStatusColor = (status) => {
    switch (status) {
      case 'connected': return 'ide';
      case 'error': return 'error';
      case 'warning': return 'warning';
      case 'success': return 'success';
      default: return 'secondaryText';
    }
  };
  
  const getStatusIcon = (status) => {
    switch (status) {
      case 'connected': return '●';
      case 'error': return '●';
      case 'warning': return '⚠';
      case 'success': return '✓';
      default: return '○';
    }
  };
  
  return (
    <Text color={getStatusColor(status)}>
      {getStatusIcon(status)} {children}
    </Text>
  );
};

// 进度条组件
export const ProgressBar = ({ progress = 0, width = 100 }) => (
  <Box 
    style={{
      width: `${width}px`,
      height: '4px',
      backgroundColor: '#444',
      borderRadius: '2px',
      overflow: 'hidden'
    }}
  >
    <div 
      style={{
        width: `${progress}%`,
        height: '100%',
        backgroundColor: colors.claude,
        transition: 'width 0.3s ease'
      }}
    />
  </Box>
);

// 代码块组件
export const CodeBlock = ({ children, language = 'text' }) => (
  <Box 
    style={{
      backgroundColor: colors.backgroundColor,
      borderRadius: '4px',
      padding: '12px',
      fontFamily: 'monospace',
      fontSize: '14px',
      border: '1px solid #555'
    }}
  >
    <Text color="text">{children}</Text>
  </Box>
);

// 按钮组件
export const Button = ({ 
  children, 
  variant = 'primary', 
  onClick, 
  disabled = false,
  ...props 
}) => {
  const getButtonStyle = (variant) => {
    const baseStyle = {
      padding: '8px 16px',
      borderRadius: '4px',
      border: 'none',
      cursor: disabled ? 'not-allowed' : 'pointer',
      fontWeight: 'bold',
      opacity: disabled ? 0.6 : 1
    };
    
    switch (variant) {
      case 'primary':
        return {
          ...baseStyle,
          backgroundColor: colors.claude,
          color: colors.white
        };
      case 'success':
        return {
          ...baseStyle,
          backgroundColor: colors.success,
          color: colors.white
        };
      case 'error':
        return {
          ...baseStyle,
          backgroundColor: colors.error,
          color: colors.white
        };
      case 'secondary':
        return {
          ...baseStyle,
          backgroundColor: 'transparent',
          color: colors.secondaryText,
          border: `1px solid ${colors.secondaryText}`
        };
      default:
        return baseStyle;
    }
  };
  
  return (
    <button 
      style={getButtonStyle(variant)}
      onClick={onClick}
      disabled={disabled}
      {...props}
    >
      {children}
    </button>
  );
};

// 输入框组件
export const Input = ({ 
  value, 
  onChange, 
  placeholder,
  disabled = false,
  style = {},
  ...props 
}) => {
  const inputStyle = {
    padding: '8px 12px',
    borderRadius: '4px',
    border: `1px solid ${colors.secondaryText}`,
    backgroundColor: colors.backgroundColor,
    color: colors.text,
    fontSize: '14px',
    outline: 'none',
    ...style
  };
  
  return (
    <input
      style={inputStyle}
      value={value}
      onChange={onChange}
      placeholder={placeholder}
      disabled={disabled}
      {...props}
    />
  );
};

// 选择框组件
export const Select = ({ options = [], value, onChange, ...props }) => {
  const selectStyle = {
    padding: '8px 12px',
    borderRadius: '4px',
    border: `1px solid ${colors.secondaryText}`,
    backgroundColor: colors.backgroundColor,
    color: colors.text,
    fontSize: '14px',
    outline: 'none'
  };
  
  return (
    <select style={selectStyle} value={value} onChange={onChange} {...props}>
      {options.map((option, index) => (
        <option key={index} value={option.value}>
          {option.label}
        </option>
      ))}
    </select>
  );
};

// 复选框组件
export const Checkbox = ({ checked, onChange, children, ...props }) => (
  <Box flexDirection="row" style={{ alignItems: 'center', gap: '8px' }}>
    <Text color={checked ? 'success' : 'secondaryText'}>
      {checked ? '☑' : '☐'}
    </Text>
    <Text>{children}</Text>
  </Box>
);

// 导出所有组件
export default {
  Text,
  Box,
  ErrorMessage,
  SuccessMessage,
  WarningMessage,
  ClaudeBrand,
  ConfigurationError,
  WelcomeScreen,
  StatusIndicator,
  ProgressBar,
  CodeBlock,
  Button,
  Input,
  Select,
  Checkbox,
  colors,
  baseStyles
};
