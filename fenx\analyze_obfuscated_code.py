#!/usr/bin/env python3
"""
Claude Code CLI.js 混淆代码分析器
分析混淆的JavaScript代码，提取UI组件、React/Ink使用模式等
"""

import re
import json
import os
from collections import defaultdict, Counter
from pathlib import Path

class ObfuscatedCodeAnalyzer:
    def __init__(self, file_path):
        self.file_path = file_path
        self.content = ""
        self.functions = defaultdict(list)
        self.strings = []
        self.imports = []
        self.react_patterns = []
        self.ink_patterns = []
        self.ui_components = []
        
    def load_file(self):
        """加载混淆的JS文件"""
        try:
            with open(self.file_path, 'r', encoding='utf-8') as f:
                self.content = f.read()
            print(f"已加载文件: {self.file_path}")
            print(f"文件大小: {len(self.content):,} 字符")
        except Exception as e:
            print(f"加载文件失败: {e}")
            return False
        return True
    
    def extract_strings(self):
        """提取所有字符串常量"""
        # 匹配单引号和双引号字符串
        string_pattern = r'["\']([^"\'\\]|\\.)*["\']'
        matches = re.findall(string_pattern, self.content[:50000])  # 只分析前50KB避免内存过大
        
        # 过滤和分类字符串
        ui_keywords = ['render', 'createElement', 'component', 'props', 'state', 
                      'flexDirection', 'color', 'padding', 'margin', 'border',
                      'Box', 'Text', 'useState', 'useEffect', 'ink', 'react']
        
        for match in matches:
            cleaned = match.strip('"\'')
            if any(keyword in cleaned.lower() for keyword in ui_keywords):
                self.strings.append(cleaned)
                
        print(f"找到 {len(self.strings)} 个UI相关字符串")
        
    def analyze_function_patterns(self):
        """分析函数模式，查找React/Ink组件"""
        # 查找函数定义模式
        # 混淆函数通常是: 字母+数字的组合，如 y2A, k2A等
        func_pattern = r'function\s+([a-zA-Z]\w{1,3})\s*\([^)]*\)\s*\{'
        matches = re.findall(func_pattern, self.content[:100000])
        
        func_counter = Counter(matches)
        print(f"找到 {len(set(matches))} 个唯一函数")
        print("最常见的函数名:", func_counter.most_common(10))
        
        # 查找React组件模式
        react_patterns = [
            r'createElement\s*\(',
            r'\.createElement\s*\(',
            r'useState\s*\(',
            r'useEffect\s*\(',
            r'\.render\s*\(',
        ]
        
        for pattern in react_patterns:
            matches = re.findall(pattern, self.content[:200000])
            if matches:
                self.react_patterns.append({
                    'pattern': pattern,
                    'count': len(matches),
                    'samples': matches[:5]
                })
                
    def extract_ui_components(self):
        """提取UI组件定义"""
        # 查找可能的组件定义
        # 寻找返回createElement的函数
        component_pattern = r'function\s+(\w+)[^{]+\{[^}]*createElement[^}]*\}'
        
        # 由于文件太大，分块处理
        chunk_size = 50000
        for i in range(0, min(len(self.content), 500000), chunk_size):
            chunk = self.content[i:i+chunk_size]
            matches = re.findall(component_pattern, chunk, re.DOTALL)
            self.ui_components.extend(matches)
            
        print(f"找到 {len(self.ui_components)} 个可能的UI组件")
        
    def analyze_imports(self):
        """分析import语句"""
        import_pattern = r'import\s*\{?([^}]+)\}?\s*from\s*["\']([^"\']+)["\']'
        matches = re.findall(import_pattern, self.content[:10000])
        
        for imports, module in matches:
            self.imports.append({
                'imports': imports.strip(),
                'module': module
            })
            
        print(f"找到 {len(self.imports)} 个import语句")
        
    def find_color_schemes(self):
        """查找颜色主题"""
        # 查找颜色值
        color_pattern = r'(?:color|Color)\s*[:=]\s*["\']([^"\']+)["\']'
        hex_pattern = r'#[0-9A-Fa-f]{3,6}'
        
        colors = []
        color_matches = re.findall(color_pattern, self.content[:200000])
        hex_matches = re.findall(hex_pattern, self.content[:200000])
        
        colors.extend(color_matches)
        colors.extend(hex_matches)
        
        return list(set(colors))
        
    def find_layout_properties(self):
        """查找布局属性"""
        layout_props = [
            'flexDirection', 'gap', 'padding', 'margin', 'border',
            'width', 'height', 'display', 'position', 'alignItems',
            'justifyContent', 'flexWrap', 'borderStyle', 'borderColor'
        ]
        
        found_props = {}
        for prop in layout_props:
            pattern = rf'{prop}\s*[:=]\s*["\']?([^,;\s\}}]+)'
            matches = re.findall(pattern, self.content[:200000], re.IGNORECASE)
            if matches:
                found_props[prop] = list(set(matches))[:10]  # 只保留前10个唯一值
                
        return found_props
        
    def analyze_all(self):
        """执行所有分析"""
        print("开始分析混淆代码...")
        print("-" * 50)
        
        self.extract_strings()
        self.analyze_function_patterns()
        self.extract_ui_components()
        self.analyze_imports()
        
        colors = self.find_color_schemes()
        layout_props = self.find_layout_properties()
        
        # 生成报告
        report = {
            'file_info': {
                'path': self.file_path,
                'size': len(self.content),
                'analyzed_size': min(len(self.content), 500000)
            },
            'ui_strings': self.strings[:50],  # 只保存前50个
            'react_patterns': self.react_patterns,
            'ui_components': list(set(self.ui_components))[:20],
            'imports': self.imports,
            'color_schemes': colors[:20],
            'layout_properties': layout_props
        }
        
        return report
        
    def save_report(self, report, output_path):
        """保存分析报告"""
        with open(output_path, 'w', encoding='utf-8') as f:
            json.dump(report, f, indent=2, ensure_ascii=False)
        print(f"\n报告已保存到: {output_path}")
        
    def generate_markdown_report(self, report, output_path):
        """生成Markdown格式的报告"""
        md_content = f"""# Claude Code CLI.js 混淆代码分析报告

## 文件信息
- 文件路径: `{report['file_info']['path']}`
- 文件大小: {report['file_info']['size']:,} 字符
- 分析范围: 前 {report['file_info']['analyzed_size']:,} 字符

## UI相关字符串
找到 {len(report['ui_strings'])} 个UI相关字符串：

```
{chr(10).join(report['ui_strings'][:20])}
...
```

## React模式分析
"""
        for pattern in report['react_patterns']:
            md_content += f"\n### {pattern['pattern']}\n"
            md_content += f"- 出现次数: {pattern['count']}\n"
            
        md_content += f"\n## UI组件\n找到 {len(report['ui_components'])} 个可能的UI组件函数：\n\n"
        md_content += "```javascript\n"
        md_content += ", ".join(report['ui_components'][:20])
        md_content += "\n```\n"
        
        md_content += "\n## 颜色主题\n"
        if report['color_schemes']:
            md_content += "```css\n"
            for color in report['color_schemes'][:10]:
                md_content += f"{color}\n"
            md_content += "```\n"
            
        md_content += "\n## 布局属性\n"
        for prop, values in report['layout_properties'].items():
            if values:
                md_content += f"\n### {prop}\n"
                md_content += "- " + ", ".join(str(v) for v in values[:5]) + "\n"
                
        with open(output_path, 'w', encoding='utf-8') as f:
            f.write(md_content)
        print(f"Markdown报告已保存到: {output_path}")

def main():
    # 设置路径
    cli_path = r"E:\claude\yuan\package\cli.js"
    output_dir = r"E:\claude\yuan\package\fenx"
    
    # 创建分析器
    analyzer = ObfuscatedCodeAnalyzer(cli_path)
    
    # 加载文件
    if not analyzer.load_file():
        return
        
    # 执行分析
    report = analyzer.analyze_all()
    
    # 保存报告
    json_path = os.path.join(output_dir, "analysis_report.json")
    md_path = os.path.join(output_dir, "analysis_report.md")
    
    analyzer.save_report(report, json_path)
    analyzer.generate_markdown_report(report, md_path)
    
    print("\n分析完成！")

if __name__ == "__main__":
    main()