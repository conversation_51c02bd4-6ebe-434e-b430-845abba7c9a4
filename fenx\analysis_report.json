{"file_info": {"path": "E:\\claude\\yuan\\package\\cli.js", "size": 8832640, "analyzed_size": 500000}, "ui_strings": [], "react_patterns": [{"pattern": "createElement\\s*\\(", "count": 1, "samples": ["createElement("]}, {"pattern": "\\.createElement\\s*\\(", "count": 1, "samples": [".createElement("]}], "ui_components": ["EsB"], "imports": [{"imports": "createRequire as whB", "module": "node:module"}], "color_schemes": [], "layout_properties": {"margin": ["<PERSON><PERSON>"]}}