{"react_components": [], "ink_components": [], "ui_functions": [], "color_theme": {"defined_colors": [], "hex_colors": [], "known_theme_colors": {"claude": true, "success": true, "error": true, "warning": true, "remember": true, "bashBorder": true, "secondaryBorder": true, "text": true, "permission": true}}, "layout_patterns": [{"property": "margin", "values": ["<PERSON><PERSON>"], "count": 1}], "state_management": [], "event_handlers": [], "createElement_patterns": ["B", "ent", "ent"], "hooks_patterns": ["useState(", "useState(", "useState(", "useEffect(", "useEffect(", "useEffect(", "useRef(", "useRef(", "useRef(", "useMemo(", "useMemo(", "useMemo(", "useCallback(", "useCallback(", "useCallback(", "ent", "y", "H"], "component_patterns": ["EsB", "Y"]}