{"extracted_functions": [{"name": "j2A", "type": "component", "description": "工具使用统计组件", "snippet": "var j2A=z((S2A)=>{Object.defineProperty(S2A,\"__esModule\",{value:!0})});var fC1=z((k2A)=>{Object.defineProperty(k2A,\"__esModule\",{value:!0});k2A.audit=..."}, {"name": "Wy2", "type": "listener", "description": "选择变化监听器", "snippet": "var Wy2=\"SENT\",Jy2=\"RECEIVED\";Xy2.MESSAGETYPEVALUES_SENT=Wy2;Xy2.MESSAGETYPEVALUES_RECEIVED=Jy2;Xy2.MessageTypeValues=mH.createConstMap([Wy2,Jy2])});v..."}, {"name": "c9", "type": "utility", "description": "终端尺寸管理器", "snippet": "var c9=G1(U1(),1);var fTB=\"Paste code here if prompted > \";function hTB({onSuccess:A,onCancel:B}){let[Q,D]=c9.useState({state:\"starting\"}),[Z]=c9.useS..."}, {"name": "Z0", "type": "listener", "description": "全局键盘监听器", "snippet": "var Z0=mB.get(H1);if(Z0!=null)if(tG.delete(Z0),B0.has(H1))B0.delete(H1),u6.add(Z0),Gq(),hA(H1);else u6.delete(Z0)}function LA(H1){Qq(H1,tG,f3)}functio..."}, {"name": "BE", "type": "component", "description": "通用内容显示组件", "snippet": "function BE(){for(;v7!==null&&!vM();)uv(v7)}function uv(w){var q=dv(w.alternate,w,sG);w.memoizedProps=w.pendingProps,q===null?SX(w):v7=q,BR.current=nu..."}, {"name": "LV2", "type": "utility", "description": "多行内容格式化", "snippet": "function LV2(A){let{socket:B,timeoutType:Q,client:D,paused:Z}=A.deref();if(Q===ts){if(!B[xk]||B.writableNeedDrain||D[vW]>1)t9(!Z,\"cannot be paused whi..."}], "ui_patterns": [{"type": "createElement", "variants": ["document.createElement", "<PERSON><PERSON>createElement"], "count": 3}, {"type": "createElement", "variants": ["createElement"], "count": 3}], "color_theme": {"claude": true, "success": true, "error": true, "warning": true, "remember": true, "bashBorder": true, "secondaryBorder": true, "text": true, "permission": true}, "layout_properties": {"margin": ["<PERSON><PERSON>"]}}