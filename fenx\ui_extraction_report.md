# Claude Code UI组件提取报告

基于逆向分析项目的知识进行精确提取

## 已找到的UI函数

| 函数名 | 类型 | 描述 | 代码片段 |
|--------|------|------|----------|
| j2A | component | 工具使用统计组件 | `var j2A=z((S2A)=>{Object.defineProperty(S2A,"__esM...` |
| Wy2 | listener | 选择变化监听器 | `var Wy2="SENT",Jy2="RECEIVED";Xy2.MESSAGETYPEVALUE...` |
| c9 | utility | 终端尺寸管理器 | `var c9=G1(U1(),1);var fTB="Paste code here if prom...` |
| Z0 | listener | 全局键盘监听器 | `var Z0=mB.get(H1);if(Z0!=null)if(tG.delete(Z0),B0....` |
| BE | component | 通用内容显示组件 | `function BE(){for(;v7!==null&&!vM();)uv(v7)}functi...` |
| LV2 | utility | 多行内容格式化 | `function LV2(A){let{socket:B,timeoutType:Q,client:...` |

## UI模式

### createElement变体
- 总调用次数: 3
- 变体: document.createElement, B.createElement

### createElement变体
- 总调用次数: 3
- 变体: createElement


## 颜色主题

已确认的主题颜色:

- [v] claude
- [v] success
- [v] error
- [v] warning
- [v] remember
- [v] bashBorder
- [v] secondaryBorder
- [v] text
- [v] permission

## 布局属性

### margin
值: B.checkinMargin

