// Claude Code UI组件完整复现示例
// 包含所有主要UI组件的实现

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text, useInput, useApp, Newline, Spacer } from 'ink';
import spinners from 'cli-spinners';

// ============ 核心组件实现 ============

// 1. 欢迎界面组件 (y2A)
export function WelcomeComponent() {
  return (
    <Box>
      <Text color="#FF6B35" bold>✻ </Text>
      <Text>Welcome to Claude Code</Text>
    </Box>
  );
}

// 2. GA统计展示组件 (k2A)
export function StatsDisplayComponent() {
  return (
    <Box flexDirection="column" gap={1} marginTop={1}>
      <Text>Claude Code is now generally available. Thank you for making it possible 🙏</Text>
      <Text>Here's a glimpse at all of the community's contributions:</Text>
    </Box>
  );
}

// 3. 工具使用统计图表组件 (j2A)
export function ToolStatsComponent({ stats, width = 60 }) {
  const defaultStats = [
    { toolName: "Read", usesTx: "47.5M", usesN: 47500000 },
    { toolName: "Edit", usesTx: "39.3M", usesN: 39300000 },
    { toolName: "Bash", usesTx: "17.9M", usesN: 17900000 },
    { toolName: "Grep", usesTx: "14.7M", usesN: 14700000 },
    { toolName: "Write", usesTx: "6.8M", usesN: 6800000 }
  ];
  
  const data = stats || defaultStats;
  const maxUses = Math.max(...data.map(d => d.usesN));
  const maxNameLength = Math.max(...data.map(d => d.toolName.length)) + 5;
  const barWidth = width - maxNameLength - 2;
  
  return (
    <Box flexDirection="column" gap={1} marginTop={1}>
      {data.map((stat, i) => {
        const nameWithPadding = stat.toolName + ' '.repeat(maxNameLength - stat.toolName.length);
        const percent = stat.usesN / maxUses;
        
        return (
          <Box key={i} flexDirection="row">
            <Text>{nameWithPadding}</Text>
            <ProgressBar 
              width={barWidth} 
              percent={percent} 
              text={stat.usesTx}
            />
          </Box>
        );
      })}
    </Box>
  );
}

// 4. 进度条组件 (Fq5)
export function ProgressBar({ width, percent, text }) {
  const filledWidth = Math.ceil(width * percent);
  const emptyWidth = width - filledWidth;
  
  // 计算文本位置
  const textPadding = Math.max(0, filledWidth - text.length - 1);
  const filledBar = ' ' + text + ' '.repeat(textPadding);
  const emptyBar = ' '.repeat(Math.max(0, emptyWidth));
  
  return (
    <Box>
      <Text backgroundColor="#FF6B35">{filledBar}</Text>
      <Text backgroundColor="#666666">{emptyBar}</Text>
    </Box>
  );
}

// 5. 选择变化监听器组件 (Wy2)
export function SelectionDisplay({ selection }) {
  if (!selection) return null;
  
  return (
    <Box borderStyle="round" borderColor="cyan" padding={1} marginTop={1}>
      <Box flexDirection="column">
        <Text color="cyan">Selected Text:</Text>
        <Text>{selection.text || 'No selection'}</Text>
        <Text dimColor>Lines: {selection.lineCount || 0}</Text>
      </Box>
    </Box>
  );
}

// 6. 信任确认对话框组件 (xy2)
export function TrustDialog({ onChoice }) {
  const [selected, setSelected] = useState(0);
  const options = ['Yes', 'No', 'Configure MCP'];
  
  useInput((input, key) => {
    if (key.upArrow) {
      setSelected((s) => Math.max(0, s - 1));
    } else if (key.downArrow) {
      setSelected((s) => Math.min(options.length - 1, s + 1));
    } else if (key.return) {
      onChoice(options[selected].toLowerCase());
    }
  });
  
  return (
    <Box borderStyle="round" borderColor="yellow" padding={1} marginTop={1}>
      <Box flexDirection="column">
        <Text color="yellow" bold>Trust this directory?</Text>
        <Newline />
        {options.map((option, i) => (
          <Text key={i} color={i === selected ? 'green' : undefined}>
            {i === selected ? '▶ ' : '  '}{option}
          </Text>
        ))}
      </Box>
    </Box>
  );
}

// 7. 终端尺寸管理Hook (c9)
export function useTerminalSize() {
  const [size, setSize] = useState({
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24
  });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        columns: process.stdout.columns || 80,
        rows: process.stdout.rows || 24
      });
    };
    
    process.stdout.on('resize', handleResize);
    process.stdout.setMaxListeners(200); // 基于源码的设置
    
    return () => {
      process.stdout.off('resize', handleResize);
    };
  }, []);
  
  return size;
}

// 8. 通用内容显示组件 (BE)
export function ContentDisplay({ content, verbose = false, isError = false }) {
  const { columns } = useTerminalSize();
  
  // 内容折叠逻辑 (X45函数的功能)
  const truncateWithIndicator = (text, maxWidth) => {
    const lines = text.split('\n');
    const maxLines = verbose ? lines.length : 10;
    const displayLines = lines.slice(0, maxLines);
    
    if (lines.length > maxLines) {
      displayLines.push(
        <Text key="more" dimColor>
          ... +{lines.length - maxLines} lines (ctrl+r to expand)
        </Text>
      );
    }
    
    return displayLines;
  };
  
  const displayContent = typeof content === 'string' 
    ? truncateWithIndicator(content, columns)
    : content;
  
  return (
    <Box flexDirection="column" marginTop={1}>
      {Array.isArray(displayContent) ? (
        displayContent.map((line, i) => (
          <Text key={i} color={isError ? 'red' : undefined}>
            {line}
          </Text>
        ))
      ) : (
        <Text color={isError ? 'red' : undefined}>
          {displayContent}
        </Text>
      )}
    </Box>
  );
}

// 9. IDE集成显示组件 (Je0)
export function IDEIntegrationDisplay({ installedVersion }) {
  const platform = process.platform;
  const ide = 'VS Code'; // 可以动态检测
  const shortcut = platform === 'darwin' ? 'Cmd+Esc' : 'Ctrl+Esc';
  
  return (
    <Box 
      flexDirection="column" 
      borderStyle="round" 
      borderColor="#666666"
      padding={1}
      marginTop={2}
    >
      <Text color="#00C851" bold>
        🎉 Claude Code extension installed in {ide}!
      </Text>
      {installedVersion && (
        <Text dimColor>Version: {installedVersion}</Text>
      )}
      <Box marginTop={1} flexDirection="column">
        <Text bold>Quick start:</Text>
        <Text>• Press {shortcut} to launch Claude Code</Text>
        <Text>• View and apply file diffs directly in your editor</Text>
        <Text>• Use Cmd+K to insert @File references</Text>
      </Box>
    </Box>
  );
}

// 10. MCP安全提示组件 (DE1)
export function MCPSecurityNotice() {
  return (
    <Box marginTop={1} padding={1}>
      <Text>
        MCP servers may execute code or access system resources. 
        All tool calls require approval. Learn more in the{' '}
        <Text color="#33B5E5" underline>MCP documentation</Text>.
      </Text>
    </Box>
  );
}

// 11. 加载/响应状态指示器
export function StatusIndicator({ status, duration }) {
  const spinner = spinners.dots;
  const [frame, setFrame] = useState(0);
  
  useEffect(() => {
    if (status === 'responding') {
      const timer = setInterval(() => {
        setFrame((f) => (f + 1) % spinner.frames.length);
      }, spinner.interval);
      return () => clearInterval(timer);
    }
  }, [status, spinner]);
  
  if (status === 'responding') {
    return (
      <Box>
        <Text color="cyan">{spinner.frames[frame]} </Text>
        <Text>Thinking</Text>
        {duration && <Text dimColor> ({duration}s)</Text>}
      </Box>
    );
  }
  
  return <Text color="green">✓ Ready</Text>;
}

// ============ 演示应用 ============
export function DemoApp() {
  const { columns } = useTerminalSize();
  const [status, setStatus] = useState('ready');
  const [showTrustDialog, setShowTrustDialog] = useState(false);
  const [selection, setSelection] = useState(null);
  
  // 模拟状态变化
  useEffect(() => {
    const timer = setTimeout(() => {
      setStatus('responding');
      setTimeout(() => setStatus('ready'), 3000);
    }, 2000);
    
    return () => clearTimeout(timer);
  }, []);
  
  // 键盘控制
  useInput((input, key) => {
    if (key.escape) {
      process.exit(0);
    } else if (input === 't') {
      setShowTrustDialog(!showTrustDialog);
    } else if (input === 's') {
      setSelection({
        text: 'Example selected text from editor',
        lineCount: 5,
        filePath: '/path/to/file.js'
      });
    }
  });
  
  return (
    <Box flexDirection="column" padding={1}>
      <WelcomeComponent />
      <StatusIndicator status={status} duration={status === 'responding' ? 2 : 0} />
      
      <StatsDisplayComponent />
      <ToolStatsComponent width={Math.min(columns - 4, 80)} />
      
      {selection && <SelectionDisplay selection={selection} />}
      
      {showTrustDialog && (
        <TrustDialog onChoice={(choice) => {
          console.log('User chose:', choice);
          setShowTrustDialog(false);
        }} />
      )}
      
      <IDEIntegrationDisplay installedVersion="1.0.61" />
      <MCPSecurityNotice />
      
      <Box marginTop={2} borderStyle="single" borderColor="dimGray" padding={1}>
        <Text dimColor>
          Press 't' for trust dialog, 's' to show selection, ESC to exit
        </Text>
      </Box>
    </Box>
  );
}

// 颜色主题定义
export const colorTheme = {
  claude: '#FF6B35',      // 品牌橙色
  success: '#00C851',     // 成功绿色
  error: '#FF4444',       // 错误红色
  warning: '#FFBB33',     // 警告黄色
  remember: '#33B5E5',    // 记忆蓝色
  bashBorder: '#AA7942',  // Bash边框色
  secondaryBorder: '#666666', // 次要边框色
  text: '#FFFFFF',        // 主文本色
  permission: '#9C27B0'   // 权限紫色
};