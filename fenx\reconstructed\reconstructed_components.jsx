// Claude Code UI组件复现
// 基于混淆代码分析结果的重建版本

import React, { useState, useEffect, useRef } from 'react';
import { Box, Text } from 'ink';

// 欢迎界面组件 (原始: y2A)
export function WelcomeComponent() {
  return (
    <Text>
      <Text color="claude">✻ </Text>
      <Text>Welcome to Claude Code</Text>
    </Text>
  );
}

// GA统计展示组件 (原始: k2A)
export function StatsDisplayComponent() {
  return (
    <Box flexDirection="column" gap={1}>
      <Text>Claude Code is now generally available. Thank you for making it possible 🙏</Text>
      <Text>Here's a glimpse at all of the community's contributions:</Text>
    </Box>
  );
}

// 工具使用统计组件 (原始: j2A)
export function ToolStatsComponent({ stats, width }) {
  const defaultStats = [
    { toolName: "Read", usesTx: "47.5M", usesN: 47500000 },
    { toolName: "Edit", usesTx: "39.3M", usesN: 39300000 },
    { toolName: "Bash", usesTx: "17.9M", usesN: 17900000 },
    { toolName: "Grep", usesTx: "14.7M", usesN: 14700000 },
    { toolName: "Write", usesTx: "6.8M", usesN: 6800000 }
  ];
  
  const data = stats || defaultStats;
  const maxUses = Math.max(...data.map(d => d.usesN));
  
  return (
    <Box flexDirection="column" gap={1}>
      {data.map((stat, i) => (
        <Box key={i} flexDirection="row">
          <Text>{stat.toolName.padEnd(10)}</Text>
          <ProgressBar 
            width={width - 15} 
            percent={stat.usesN / maxUses} 
            text={stat.usesTx}
          />
        </Box>
      ))}
    </Box>
  );
}

// 进度条组件 (原始: Fq5)
export function ProgressBar({ width, percent, text }) {
  const filledWidth = Math.ceil(width * percent);
  const emptyWidth = width - filledWidth;
  
  const filledBar = text + ' '.repeat(Math.max(0, filledWidth - text.length - 1));
  const emptyBar = ' '.repeat(Math.max(0, emptyWidth));
  
  return (
    <Text>
      <Text backgroundColor="claude">{filledBar}</Text>
      <Text backgroundColor="secondaryBorder">{emptyBar}</Text>
    </Text>
  );
}

// 终端尺寸管理Hook (原始: c9)
export function useTerminalSize() {
  const [size, setSize] = useState({
    columns: process.stdout.columns || 80,
    rows: process.stdout.rows || 24
  });
  
  useEffect(() => {
    const handleResize = () => {
      setSize({
        columns: process.stdout.columns || 80,
        rows: process.stdout.rows || 24
      });
    };
    
    process.stdout.on('resize', handleResize);
    return () => process.stdout.off('resize', handleResize);
  }, []);
  
  return size;
}

// 通用内容显示组件 (原始: BE)
export function ContentDisplay({ content, verbose = false, isError = false }) {
  const { columns } = useTerminalSize();
  
  const displayContent = verbose 
    ? content 
    : truncateContent(content, columns);
    
  return (
    <Box>
      <Text color={isError ? 'error' : undefined}>
        {displayContent}
      </Text>
    </Box>
  );
}

function truncateContent(content, maxWidth) {
  if (content.length <= maxWidth) return content;
  return content.slice(0, maxWidth - 3) + '...';
}

// 颜色主题定义
export const colorTheme = {
  claude: '#FF6B35',
  success: '#00C851',
  error: '#FF4444',  
  warning: '#FFBB33',
  remember: '#33B5E5',
  bashBorder: '#AA7942',
  secondaryBorder: '#666666',
  text: '#FFFFFF',
  permission: '#9C27B0'
};
