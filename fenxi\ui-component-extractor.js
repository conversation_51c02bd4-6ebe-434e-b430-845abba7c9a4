#!/usr/bin/env node

/**
 * Claude Code UI组件提取器
 * 专门用于提取和分析UI相关的组件和样式信息
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class UIComponentExtractor {
    constructor() {
        // UI组件相关的正则表达式
        this.patterns = {
            // React组件模式
            reactComponents: /createElement\s*\(\s*["']?(\w+)["']?\s*,\s*\{([^}]*)\}/gi,
            
            // 样式属性模式
            styleProps: /(color|backgroundColor|fontSize|fontWeight|padding|margin|width|height|display|flexDirection|alignItems|justifyContent|border|borderRadius):\s*["']?([^,"'}]+)["']?/gi,
            
            // 颜色定义模式
            colorDefinitions: /(color|backgroundColor):\s*["']([^"']+)["']/gi,
            
            // 布局属性模式
            layoutProps: /(flexDirection|alignItems|justifyContent|gap|padding|margin|width|height|minWidth|maxWidth|minHeight|maxHeight):\s*["']?([^,"'}]+)["']?/gi,
            
            // 交互状态模式
            interactionStates: /(hover|focus|active|disabled|selected|pressed):\s*\{([^}]*)\}/gi,
            
            // 主题相关模式
            themeProps: /(theme|dark|light|primary|secondary|accent|error|warning|success|info)["']?\s*[:=]/gi,
            
            // 动画相关模式
            animationProps: /(animation|transition|transform|opacity|scale):\s*["']?([^,"'}]+)["']?/gi,
            
            // 响应式设计模式
            responsiveProps: /(mobile|tablet|desktop|sm|md|lg|xl):\s*\{([^}]*)\}/gi,
            
            // 可访问性属性模式
            a11yProps: /(aria-\w+|role|tabIndex|alt|title):\s*["']([^"']+)["']/gi,
            
            // 事件处理模式
            eventHandlers: /(onClick|onKeyPress|onFocus|onBlur|onChange|onSubmit|onMouseEnter|onMouseLeave):\s*([^,}]+)/gi
        };
        
        // UI组件类型分类
        this.componentTypes = {
            layout: ['div', 'section', 'article', 'header', 'footer', 'main', 'aside'],
            text: ['p', 'span', 'h1', 'h2', 'h3', 'h4', 'h5', 'h6', 'label'],
            input: ['input', 'textarea', 'select', 'button'],
            media: ['img', 'video', 'audio', 'canvas', 'svg'],
            interactive: ['a', 'button', 'details', 'summary'],
            form: ['form', 'fieldset', 'legend', 'input', 'textarea', 'select', 'button'],
            list: ['ul', 'ol', 'li', 'dl', 'dt', 'dd'],
            table: ['table', 'thead', 'tbody', 'tr', 'th', 'td']
        };
    }

    /**
     * 提取UI组件信息
     * @param {string} filePath 文件路径
     * @returns {Object} 提取结果
     */
    extractUIComponents(filePath) {
        console.log(`\n🎨 提取UI组件: ${path.basename(filePath)}`);
        
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return null;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const results = {
            file: filePath,
            components: [],
            styles: [],
            colors: [],
            layouts: [],
            interactions: [],
            themes: [],
            animations: [],
            responsive: [],
            accessibility: [],
            events: [],
            statistics: {
                totalComponents: 0,
                componentsByType: {},
                uniqueColors: new Set(),
                styleProperties: new Set()
            }
        };

        // 提取React组件
        this.extractReactComponents(content, results);
        
        // 提取样式属性
        this.extractStyleProperties(content, results);
        
        // 提取颜色信息
        this.extractColors(content, results);
        
        // 提取布局信息
        this.extractLayouts(content, results);
        
        // 提取交互状态
        this.extractInteractions(content, results);
        
        // 提取主题信息
        this.extractThemes(content, results);
        
        // 提取动画信息
        this.extractAnimations(content, results);
        
        // 提取响应式设计
        this.extractResponsive(content, results);
        
        // 提取可访问性属性
        this.extractAccessibility(content, results);
        
        // 提取事件处理
        this.extractEventHandlers(content, results);
        
        // 计算统计信息
        this.calculateStatistics(results);

        return results;
    }

    /**
     * 提取React组件
     */
    extractReactComponents(content, results) {
        let match;
        while ((match = this.patterns.reactComponents.exec(content)) !== null) {
            const componentName = match[1];
            const props = match[2];
            
            results.components.push({
                name: componentName,
                props: props.trim(),
                type: this.categorizeComponent(componentName),
                context: this.getContext(content, match.index, 100)
            });
        }
    }

    /**
     * 提取样式属性
     */
    extractStyleProperties(content, results) {
        let match;
        while ((match = this.patterns.styleProps.exec(content)) !== null) {
            results.styles.push({
                property: match[1],
                value: match[2],
                context: this.getContext(content, match.index, 50)
            });
            results.statistics.styleProperties.add(match[1]);
        }
    }

    /**
     * 提取颜色信息
     */
    extractColors(content, results) {
        let match;
        while ((match = this.patterns.colorDefinitions.exec(content)) !== null) {
            const colorValue = match[2];
            results.colors.push({
                property: match[1],
                value: colorValue,
                context: this.getContext(content, match.index, 50)
            });
            results.statistics.uniqueColors.add(colorValue);
        }
    }

    /**
     * 提取布局信息
     */
    extractLayouts(content, results) {
        let match;
        while ((match = this.patterns.layoutProps.exec(content)) !== null) {
            results.layouts.push({
                property: match[1],
                value: match[2],
                context: this.getContext(content, match.index, 50)
            });
        }
    }

    /**
     * 提取交互状态
     */
    extractInteractions(content, results) {
        let match;
        while ((match = this.patterns.interactionStates.exec(content)) !== null) {
            results.interactions.push({
                state: match[1],
                styles: match[2],
                context: this.getContext(content, match.index, 80)
            });
        }
    }

    /**
     * 提取主题信息
     */
    extractThemes(content, results) {
        let match;
        while ((match = this.patterns.themeProps.exec(content)) !== null) {
            results.themes.push({
                match: match[0],
                context: this.getContext(content, match.index, 60)
            });
        }
    }

    /**
     * 提取动画信息
     */
    extractAnimations(content, results) {
        let match;
        while ((match = this.patterns.animationProps.exec(content)) !== null) {
            results.animations.push({
                property: match[1],
                value: match[2],
                context: this.getContext(content, match.index, 50)
            });
        }
    }

    /**
     * 提取响应式设计
     */
    extractResponsive(content, results) {
        let match;
        while ((match = this.patterns.responsiveProps.exec(content)) !== null) {
            results.responsive.push({
                breakpoint: match[1],
                styles: match[2],
                context: this.getContext(content, match.index, 80)
            });
        }
    }

    /**
     * 提取可访问性属性
     */
    extractAccessibility(content, results) {
        let match;
        while ((match = this.patterns.a11yProps.exec(content)) !== null) {
            results.accessibility.push({
                attribute: match[1],
                value: match[2],
                context: this.getContext(content, match.index, 50)
            });
        }
    }

    /**
     * 提取事件处理
     */
    extractEventHandlers(content, results) {
        let match;
        while ((match = this.patterns.eventHandlers.exec(content)) !== null) {
            results.events.push({
                event: match[1],
                handler: match[2].trim(),
                context: this.getContext(content, match.index, 60)
            });
        }
    }

    /**
     * 组件分类
     */
    categorizeComponent(componentName) {
        const lowerName = componentName.toLowerCase();
        for (const [type, components] of Object.entries(this.componentTypes)) {
            if (components.includes(lowerName)) {
                return type;
            }
        }
        return 'custom';
    }

    /**
     * 获取上下文
     */
    getContext(content, index, length = 50) {
        const start = Math.max(0, index - length);
        const end = Math.min(content.length, index + length);
        return content.substring(start, end).replace(/\n/g, '\\n');
    }

    /**
     * 计算统计信息
     */
    calculateStatistics(results) {
        results.statistics.totalComponents = results.components.length;
        
        // 按类型统计组件
        results.components.forEach(comp => {
            const type = comp.type;
            results.statistics.componentsByType[type] = 
                (results.statistics.componentsByType[type] || 0) + 1;
        });
        
        // 转换Set为数组以便序列化
        results.statistics.uniqueColors = Array.from(results.statistics.uniqueColors);
        results.statistics.styleProperties = Array.from(results.statistics.styleProperties);
    }

    /**
     * 生成报告
     */
    generateReport(results) {
        if (!results) return;

        console.log(`\n📊 UI组件分析报告`);
        console.log(`文件: ${path.basename(results.file)}`);
        console.log(`总组件数: ${results.statistics.totalComponents}`);
        
        if (Object.keys(results.statistics.componentsByType).length > 0) {
            console.log(`\n📦 组件类型分布:`);
            Object.entries(results.statistics.componentsByType)
                .sort(([,a], [,b]) => b - a)
                .forEach(([type, count]) => {
                    console.log(`   ${type}: ${count}个`);
                });
        }

        if (results.statistics.uniqueColors.length > 0) {
            console.log(`\n🎨 发现的颜色 (${results.statistics.uniqueColors.length}种):`);
            results.statistics.uniqueColors.slice(0, 10).forEach((color, index) => {
                console.log(`   ${index + 1}. ${color}`);
            });
        }

        if (results.statistics.styleProperties.length > 0) {
            console.log(`\n🎯 样式属性 (${results.statistics.styleProperties.length}种):`);
            results.statistics.styleProperties.slice(0, 10).forEach((prop, index) => {
                console.log(`   ${index + 1}. ${prop}`);
            });
        }

        if (results.interactions.length > 0) {
            console.log(`\n⚡ 交互状态 (${results.interactions.length}个):`);
            results.interactions.slice(0, 5).forEach((interaction, index) => {
                console.log(`   ${index + 1}. ${interaction.state}`);
            });
        }

        if (results.accessibility.length > 0) {
            console.log(`\n♿ 可访问性属性 (${results.accessibility.length}个):`);
            results.accessibility.slice(0, 5).forEach((a11y, index) => {
                console.log(`   ${index + 1}. ${a11y.attribute}: ${a11y.value}`);
            });
        }
    }

    /**
     * 保存结果
     */
    saveResults(results, outputPath) {
        const reportData = {
            timestamp: new Date().toISOString(),
            analysis: results,
            summary: {
                totalComponents: results.statistics.totalComponents,
                componentTypes: Object.keys(results.statistics.componentsByType).length,
                uniqueColors: results.statistics.uniqueColors.length,
                styleProperties: results.statistics.styleProperties.length,
                interactions: results.interactions.length,
                accessibility: results.accessibility.length
            }
        };

        fs.writeFileSync(outputPath, JSON.stringify(reportData, null, 2), 'utf8');
        console.log(`\n💾 UI分析结果已保存到: ${outputPath}`);
    }
}

// 主函数
function main() {
    console.log('🎨 Claude Code UI组件提取器');
    console.log('============================');

    const extractor = new UIComponentExtractor();
    const cliPath = path.join(__dirname, '..', 'cli.js');

    if (fs.existsSync(cliPath)) {
        const results = extractor.extractUIComponents(cliPath);
        if (results) {
            extractor.generateReport(results);
            
            const outputPath = path.join(__dirname, 'ui-components-analysis.json');
            extractor.saveResults(results, outputPath);
        }
    } else {
        console.log(`⚠️ 文件不存在: ${cliPath}`);
    }

    console.log('\n✅ UI组件提取完成!');
}

// 运行主函数
main();

export default UIComponentExtractor;
