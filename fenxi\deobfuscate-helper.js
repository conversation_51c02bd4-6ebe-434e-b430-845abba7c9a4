#!/usr/bin/env node

/**
 * Claude Code 反混淆辅助工具
 * 用于分析混淆后的JavaScript代码，提取有用的信息
 */

import fs from 'fs';
import path from 'path';
import { fileURLToPath } from 'url';

const __filename = fileURLToPath(import.meta.url);
const __dirname = path.dirname(__filename);

class CodeAnalyzer {
    constructor() {
        this.uiPatterns = [
            // UI相关的模式
            /color["\s]*[:=]["\s]*["'](\w+)["']/gi,
            /style["\s]*[:=]/gi,
            /render\w*["\s]*[:=]/gi,
            /display["\s]*[:=]/gi,
            /format["\s]*[:=]/gi,
            /terminal["\s]*[:=]/gi,
            /console["\s]*[:=]/gi,
            /interface["\s]*[:=]/gi,
            /flexDirection["\s]*[:=]/gi,
            /height["\s]*[:=]/gi,
            /width["\s]*[:=]/gi,
            /bold["\s]*[:=]/gi,
            /dimColor["\s]*[:=]/gi,
            /strikethrough["\s]*[:=]/gi,
        ];
        
        this.functionPatterns = [
            // 函数模式
            /function\s+(\w+)\s*\(/gi,
            /(\w+)\s*[:=]\s*function/gi,
            /(\w+)\s*[:=]\s*\([^)]*\)\s*=>/gi,
            /async\s+function\s+(\w+)/gi,
        ];
        
        this.componentPatterns = [
            // React组件模式
            /createElement\s*\(\s*["']?(\w+)["']?/gi,
            /\.createElement\s*\(/gi,
            /React\.createElement/gi,
            /jsx\s*\(/gi,
        ];
        
        this.stringPatterns = [
            // 重要字符串模式
            /"([^"]*(?:color|style|render|display|format|terminal|console|interface)[^"]*)"/gi,
            /'([^']*(?:color|style|render|display|format|terminal|console|interface)[^']*)'/gi,
        ];
    }

    /**
     * 分析代码文件
     * @param {string} filePath 文件路径
     * @returns {Object} 分析结果
     */
    analyzeFile(filePath) {
        console.log(`\n=== 分析文件: ${filePath} ===`);
        
        if (!fs.existsSync(filePath)) {
            console.error(`文件不存在: ${filePath}`);
            return null;
        }

        const content = fs.readFileSync(filePath, 'utf8');
        const results = {
            file: filePath,
            uiElements: [],
            functions: [],
            components: [],
            strings: [],
            statistics: {
                totalLines: content.split('\n').length,
                totalChars: content.length,
                minifiedLevel: this.calculateMinificationLevel(content)
            }
        };

        // 分析UI相关元素
        this.uiPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                results.uiElements.push({
                    pattern: pattern.source,
                    match: match[0],
                    context: this.getContext(content, match.index, 50)
                });
            }
        });

        // 分析函数
        this.functionPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                results.functions.push({
                    name: match[1] || 'anonymous',
                    match: match[0],
                    context: this.getContext(content, match.index, 30)
                });
            }
        });

        // 分析React组件
        this.componentPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                results.components.push({
                    match: match[0],
                    component: match[1] || 'unknown',
                    context: this.getContext(content, match.index, 40)
                });
            }
        });

        // 分析重要字符串
        this.stringPatterns.forEach(pattern => {
            let match;
            while ((match = pattern.exec(content)) !== null) {
                results.strings.push({
                    string: match[1],
                    context: this.getContext(content, match.index, 30)
                });
            }
        });

        return results;
    }

    /**
     * 获取匹配位置的上下文
     * @param {string} content 文件内容
     * @param {number} index 匹配位置
     * @param {number} contextLength 上下文长度
     * @returns {string} 上下文字符串
     */
    getContext(content, index, contextLength = 50) {
        const start = Math.max(0, index - contextLength);
        const end = Math.min(content.length, index + contextLength);
        return content.substring(start, end).replace(/\n/g, '\\n');
    }

    /**
     * 计算代码混淆程度
     * @param {string} content 文件内容
     * @returns {string} 混淆程度描述
     */
    calculateMinificationLevel(content) {
        const avgLineLength = content.length / content.split('\n').length;
        const hasSpaces = content.includes('  '); // 检查是否有多个空格
        const hasComments = content.includes('//') || content.includes('/*');
        
        if (avgLineLength > 500 && !hasSpaces && !hasComments) {
            return '高度混淆';
        } else if (avgLineLength > 200) {
            return '中度混淆';
        } else {
            return '轻度混淆或未混淆';
        }
    }

    /**
     * 生成分析报告
     * @param {Object} results 分析结果
     */
    generateReport(results) {
        if (!results) return;

        console.log(`\n📊 统计信息:`);
        console.log(`   总行数: ${results.statistics.totalLines}`);
        console.log(`   总字符数: ${results.statistics.totalChars}`);
        console.log(`   混淆程度: ${results.statistics.minifiedLevel}`);

        if (results.uiElements.length > 0) {
            console.log(`\n🎨 UI相关元素 (${results.uiElements.length}个):`);
            results.uiElements.slice(0, 10).forEach((element, index) => {
                console.log(`   ${index + 1}. ${element.match}`);
                console.log(`      上下文: ...${element.context}...`);
            });
            if (results.uiElements.length > 10) {
                console.log(`   ... 还有 ${results.uiElements.length - 10} 个元素`);
            }
        }

        if (results.functions.length > 0) {
            console.log(`\n⚙️ 函数 (${results.functions.length}个):`);
            const uniqueFunctions = [...new Set(results.functions.map(f => f.name))];
            uniqueFunctions.slice(0, 15).forEach((name, index) => {
                console.log(`   ${index + 1}. ${name}`);
            });
            if (uniqueFunctions.length > 15) {
                console.log(`   ... 还有 ${uniqueFunctions.length - 15} 个函数`);
            }
        }

        if (results.components.length > 0) {
            console.log(`\n🧩 React组件 (${results.components.length}个):`);
            results.components.slice(0, 10).forEach((comp, index) => {
                console.log(`   ${index + 1}. ${comp.match}`);
            });
        }

        if (results.strings.length > 0) {
            console.log(`\n📝 重要字符串 (${results.strings.length}个):`);
            results.strings.slice(0, 10).forEach((str, index) => {
                console.log(`   ${index + 1}. "${str.string}"`);
            });
        }
    }

    /**
     * 保存分析结果到文件
     * @param {Object} results 分析结果
     * @param {string} outputPath 输出文件路径
     */
    saveResults(results, outputPath) {
        // 检查结果是否有效
        if (!results || !results.uiElements) {
            console.error('无效的分析结果，无法保存');
            return;
        }

        const reportData = {
            timestamp: new Date().toISOString(),
            analysis: results,
            summary: {
                uiElementsCount: results.uiElements ? results.uiElements.length : 0,
                functionsCount: results.functions ? results.functions.length : 0,
                componentsCount: results.components ? results.components.length : 0,
                stringsCount: results.strings ? results.strings.length : 0,
                minificationLevel: results.statistics ? results.statistics.minifiedLevel : 'unknown'
            }
        };

        fs.writeFileSync(outputPath, JSON.stringify(reportData, null, 2), 'utf8');
        console.log(`\n💾 分析结果已保存到: ${outputPath}`);
    }
}

// 主函数
function main() {
    console.log('🔍 Claude Code 反混淆分析工具');
    console.log('================================');

    const analyzer = new CodeAnalyzer();
    const cliPath = path.join(__dirname, '..', 'cli.js');
    const sdkPath = path.join(__dirname, '..', 'sdk.mjs');

    // 分析主要文件
    const files = [cliPath, sdkPath];
    const allResults = [];

    files.forEach(filePath => {
        if (fs.existsSync(filePath)) {
            const results = analyzer.analyzeFile(filePath);
            if (results) {
                analyzer.generateReport(results);
                allResults.push(results);
            }
        } else {
            console.log(`⚠️ 文件不存在: ${filePath}`);
        }
    });

    // 保存综合分析结果
    if (allResults.length > 0) {
        const combinedResults = {
            files: allResults,
            summary: {
                totalFiles: allResults.length,
                totalUIElements: allResults.reduce((sum, r) => sum + r.uiElements.length, 0),
                totalFunctions: allResults.reduce((sum, r) => sum + r.functions.length, 0),
                totalComponents: allResults.reduce((sum, r) => sum + r.components.length, 0),
                totalStrings: allResults.reduce((sum, r) => sum + r.strings.length, 0)
            }
        };

        const outputPath = path.join(__dirname, 'analysis-results.json');
        analyzer.saveResults(combinedResults, outputPath);

        console.log('\n📋 综合分析结果:');
        console.log(`   分析文件数: ${combinedResults.summary.totalFiles}`);
        console.log(`   UI元素总数: ${combinedResults.summary.totalUIElements}`);
        console.log(`   函数总数: ${combinedResults.summary.totalFunctions}`);
        console.log(`   组件总数: ${combinedResults.summary.totalComponents}`);
        console.log(`   重要字符串总数: ${combinedResults.summary.totalStrings}`);
    }

    console.log('\n✅ 分析完成!');
}

// 如果直接运行此脚本
console.log('Script URL:', import.meta.url);
console.log('Process argv[1]:', process.argv[1]);
main();

export default CodeAnalyzer;
